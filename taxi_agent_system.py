from typing import Dict, List, Optional, Any
from datetime import datetime
import json
import os
from openai import OpenAI
from amap_mcp_tools import AmapMCPTools, mcp_geocode_address, mcp_get_city_code, mcp_search_poi, mcp_reverse_geocode_poi, mcp_recommend_similar_poi, mcp_calculate_driving_route, mcp_calculate_poi_to_poi_route, mcp_search_taxi_spots, mcp_estimate_taxi_price
from langflow_api_V4 import call_taxi_service
from parameter_validation import FunctionParameterManager, ParameterState
from dataclasses import dataclass
from enum import Enum

# 环境变量配置
DEFAULT_LONGITUDE = float(os.getenv("DEFAULT_LONGITUDE", "116.306345"))  # 默认经度
DEFAULT_LATITUDE = float(os.getenv("DEFAULT_LATITUDE", "40.040377"))     # 默认纬度
DEFAULT_CITY = os.getenv("DEFAULT_CITY", "北京")                         # 默认城市
LOCATION_CONTEXT_ENABLED = os.getenv("LOCATION_CONTEXT_ENABLED", "true").lower() == "true"

# 任务管理相关数据结构
class TaskParameterState(Enum):
    """任务参数状态枚举"""
    NOT_FILLED = "未填写"
    NOT_VALIDATED = "未校验"
    VALIDATED_PENDING = "已校验待确认"
    CONFIRMED = "已确认"

@dataclass
class TaskParameter:
    """任务参数信息"""
    name: str
    value: Any
    state: TaskParameterState
    validation_result: Optional[Dict] = None
    confusion_score: Optional[float] = None
    candidates: Optional[List] = None

@dataclass
class TaskInfo:
    """任务信息"""
    task_id: str
    function_name: str
    parameters: Dict[str, TaskParameter]
    fault_tolerance: str  # "高" 或 "低"
    created_time: str
    updated_time: str
    execution_time: Optional[str] = None
    execution_result: Optional[Dict] = None
    status: str = "pending"  # pending, executing, completed, failed

class StateManager:
    """全局状态管理器"""
    def __init__(self):
        self.conversation_state = {}
        self.api_results = {}
        self.missing_params = {}
        self.execution_history = []
        self.current_status = {
            'last_action': None,
            'status': 'ready',  # ready, processing, success, failed
            'error': None
        }
        
    def log_execution(self, action: str, status: str, details: Dict):
        """记录执行历史"""
        entry = {
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'status': status,
            'details': details
        }
        self.execution_history.append(entry)
        self.current_status = {
            'last_action': action,
            'status': status,
            'error': details.get('error')
        }
        
    def update_state(self, key: str, value: any):
        self.conversation_state[key] = {
            'value': value,
            'timestamp': datetime.now().isoformat()
        }
    
    def log_api_call(self, api_name: str, result: Dict):
        self.api_results[api_name] = result
        
    def track_missing_param(self, param: str, question: str):
        self.missing_params[param] = question


class ToolAgent:
    """集成百炼function calling的工具调用Agent"""
    def __init__(self, state_manager: StateManager):
        self.state = state_manager
        # self.client = BailianClient()
        
    async def call_api(self, endpoint: str, params: Dict) -> Dict:
        try:
            # 记录开始状态
            self.state.log_execution(
                action=f"call_api:{endpoint}",
                status="processing",
                details={"params": params}
            )
            
            # 调用百炼function calling
            func_call = {
                "name": endpoint,
                "parameters": params
            }
            response = await self.client.function_calling(func_call)
            
            # 处理响应
            if response.status == "success":
                result = {
                    "status": True,
                    "data": response.data
                }
                self.state.log_api_call(endpoint, result)
                self.state.log_execution(
                    action=f"call_api:{endpoint}",
                    status="success",
                    details={"response": result}
                )
                return result
            else:
                # TODO: 要增加 执行错误的日志记录
                raise Exception(response.error)
                
        except Exception as e:
            error_result = {
                "status": False,
                "error": str(e)
            }
            self.state.log_execution(
                action=f"call_api:{endpoint}",
                status="failed",
                details={"error": str(e)}
            )
            return error_result

class CompletionAgent:
    """条件补全Agent""" 
    def __init__(self, state_manager: StateManager):
        self.state = state_manager
        
    def generate_clarification(self, missing: Dict) -> str:
        questions = []
        for param, question in missing.items():
            questions.append(f"{question} (需要{param})")
        return "请提供以下信息:\n" + "\n".join(questions)

class DebugAgent:
    """生活助手Debug Agent - 支持5种用户场景"""
    def __init__(self, state_manager: StateManager, bailian_client=None):
        self.state = state_manager
        self.bailian_client = bailian_client
        self.error_log = []
        self.user_scenarios = {
            "new_user": "首次使用信息不全",
            "returning_user": "有明确历史记录", 
            "exploring_user": "无明确目标探索",
            "random_user": "不着边际的请求",
            "out_of_scope": "超出能力范围"
        }
        self.function_debug_log = []

    def step1_pre_validation(self, function_name: str, args: dict, user_intent: str, session_id: str = "default") -> dict:
        """步骤1: 增强的前置验证，支持用户场景分析"""
        validation_result = {
            "is_valid": True,
            "missing_params": [],
            "error_code": 1,  # 1: 完全成功, 0.5: 成功但有警告, 0: 缺少参数, -1: 函数解析错误
            "issues": [],
            "user_scenario": None
        }

        # 检测用户场景
        user_scenario = self._detect_user_scenario(session_id, user_intent)
        validation_result["user_scenario"] = user_scenario

        # 检查函数是否存在
        available_functions = ["mcp_geocode_address", "mcp_get_city_code", "mcp_search_poi", "mcp_reverse_geocode_poi", "mcp_recommend_similar_poi", "mcp_calculate_driving_route", "mcp_calculate_poi_to_poi_route", "mcp_search_taxi_spots", "mcp_estimate_taxi_price", "call_taxi_service", "confirm_parameter", "cancel_parameter"]
        if function_name not in available_functions:
            validation_result.update({
                "is_valid": False,
                "error_code": -1,
                "issues": [f"未知函数: {function_name}"]
            })
            return validation_result

        # 检查必需参数
        required_params = self._get_required_params(function_name)
        missing = []

        for param in required_params:
            if param not in args or not args[param] or str(args[param]).strip() == "":
                missing.append(param)

        if missing:
            validation_result.update({
                "is_valid": False,
                "missing_params": missing,
                "error_code": 0,
                "issues": [f"缺少必需参数: {', '.join(missing)}"]
            })

        # 检查参数合理性
        reasonableness_check = self._check_parameter_reasonableness(function_name, args, user_intent)
        if not reasonableness_check["is_reasonable"]:
            validation_result.update({
                "is_valid": False,
                "error_code": 0,
                "issues": reasonableness_check["issues"]
            })

        # 根据用户场景调整验证严格程度
        if user_scenario == "new_user" and missing:
            validation_result["error_code"] = 0.5  # 对新用户更宽容
            validation_result["issues"].append("新用户可能需要更多引导")

        return validation_result

    def step2_parameter_completion(self, function_name: str, missing_params: list, user_intent: str, session_id: str) -> dict:
        """步骤2: 参数补全 - 根据用户场景生成澄清问题"""
        if not self.bailian_client or not missing_params:
            return {"clarification_needed": False, "question": ""}

        try:
            # 获取用户场景
            user_scenario = self._detect_user_scenario(session_id, user_intent)
            
            # 根据场景定制提示词
            scenario_prompts = {
                "new_user": "这是用户第一次使用系统，请用更详细的解释和示例",
                "returning_user": "用户有使用经验，可以直接询问",
                "exploring_user": "用户可能在探索功能，提供更多引导",
                "random_user": "用户可能不清楚需求，需要明确询问",
                "out_of_scope": "用户请求可能超出范围，需要友好解释"
            }
            
            scenario_instruction = scenario_prompts.get(user_scenario, "")

            # 生成针对性的澄清问题
            prompt = f"""用户想要使用{function_name}功能，但缺少以下参数：{', '.join(missing_params)}
        用户原始意图：{user_intent}
        用户场景：{user_scenario} - {scenario_instruction}

        请生成一个简洁的澄清问题来获取缺失信息。要求：
        1. 问题要简短明了，适合语音输出
        2. 一次只问最关键的1-2个参数
        3. 根据用户场景调整语气和详细程度
        4. 提供具体的例子帮助用户理解

        只返回问题内容，不要其他解释。"""

            response = self.bailian_client.chat.completions.create(
                model="qwen-max",
                messages=[{"role": "user", "content": prompt}],
                max_tokens=100,
                temperature=0.3
            )

            question = response.choices[0].message.content.strip()

            return {
                "clarification_needed": True,
                "question": question,
                "missing_params": missing_params,
                "user_scenario": user_scenario
            }

        except Exception as e:
            # 回退到预设问题
            return {
                **self._get_fallback_clarification(function_name, missing_params),
                "user_scenario": "default"
            }

    def step3_execute_function(self, function_name: str, args: dict) -> dict:
        """步骤3: 执行函数调用"""
        try:
            # 这里会调用实际的函数执行逻辑
            # 在主类中实现具体的函数调用
            return {"status": "ready_to_execute", "function_name": function_name, "args": args}
        except Exception as e:
            return {"status": "execution_failed", "error": str(e)}

    def step4_result_analysis(self, function_name: str, args: dict, result: dict, user_intent: str, session_id: str = "default") -> dict:
        """步骤4: 结果分析和候选项提供 - 根据用户场景优化"""
        analysis = {
            "success": result.get("status", False),
            "candidates": [],
            "suggestions": [],
            "need_clarification": False,
            "user_scenario": None
        }

        # 获取用户场景
        user_scenario = self._detect_user_scenario(session_id, user_intent)
        analysis["user_scenario"] = user_scenario

        if not result.get("status", False):
            # 执行失败，根据用户场景提供错误分析和建议
            analysis["suggestions"] = self._analyze_failure_and_suggest(function_name, args, result, user_intent)
            
            # 根据用户场景调整建议
            if user_scenario == "new_user":
                analysis["suggestions"].append("作为新用户，您可以尝试更简单的查询")
            elif user_scenario == "returning_user":
                analysis["suggestions"].append("您可以参考之前的成功查询")
            elif user_scenario == "exploring_user":
                analysis["suggestions"].append("您可以尝试不同的搜索关键词")
                
            return analysis

        # 成功执行，分析结果并提供候选项
        if function_name == "mcp_search_poi":
            analysis["candidates"] = self._extract_poi_candidates(result)
            # 根据用户场景调整候选项数量
            if user_scenario == "new_user":
                analysis["candidates"] = analysis["candidates"][:3]  # 新用户只显示前3个
        elif function_name == "mcp_reverse_geocode_poi":
            analysis["candidates"] = self._extract_nearby_poi_candidates(result)
            analysis["suggestions"] = self._analyze_reverse_geocode_result(result, user_intent)
        elif function_name == "mcp_recommend_similar_poi":
            analysis["candidates"] = self._extract_similar_poi_candidates(result)
            analysis["suggestions"] = self._analyze_poi_recommendation_result(result, user_intent)
        elif function_name == "call_taxi_service":
            analysis["suggestions"] = self._analyze_taxi_result(result, user_intent)
        elif function_name == "mcp_geocode_address":
            analysis["suggestions"] = self._analyze_location_result(result, user_intent)

        # 常识性检查
        reasonableness = self._check_result_reasonableness(function_name, args, result, user_intent)
        if not reasonableness["is_reasonable"]:
            analysis["need_clarification"] = True
            analysis["suggestions"].extend(reasonableness["suggestions"])

            # 根据用户场景调整澄清方式
            if user_scenario == "new_user":
                analysis["suggestions"].append("作为新用户，您可以提供更具体的位置信息")
            elif user_scenario == "random_user":
                analysis["suggestions"].append("请确认您的查询意图")

        return analysis

    def _get_required_params(self, function_name: str) -> list:
        """获取函数的必需参数"""
        param_map = {
            "mcp_geocode_address": ["address"],
            "mcp_get_city_code": ["city_name"],
            "mcp_search_poi": ["keyword"],
            "mcp_reverse_geocode_poi": ["longitude", "latitude"],
            "mcp_recommend_similar_poi": ["poi_name"],
            "mcp_calculate_driving_route": ["origin_lng", "origin_lat", "dest_lng", "dest_lat"],
            "mcp_calculate_poi_to_poi_route": ["origin_poi", "dest_poi"],
            "mcp_search_taxi_spots": ["location"],
            "mcp_estimate_taxi_price": ["origin_poi", "dest_poi"],
            "call_taxi_service": ["start_place", "end_place"],
            "confirm_parameter": ["function_name", "parameter_name", "parameter_value"],
            "cancel_parameter": ["function_name", "parameter_name"]
        }
        return param_map.get(function_name, [])

    def _detect_user_scenario(self, session_id: str, user_intent: str) -> str:
        """识别用户场景类型"""
        # 简单实现 - 实际应根据会话历史更智能判断
        if "session_history" not in self.state.conversation_state.get(session_id, {}):
            return "new_user"
        return "default"

    def _classify_error_type(self, error: str) -> str:
        """分类错误类型"""
        error_lower = error.lower()

        if any(keyword in error_lower for keyword in ['api_key', 'authentication', 'unauthorized']):
            return "API_AUTH_ERROR"
        elif any(keyword in error_lower for keyword in ['network', 'connection', 'timeout']):
            return "NETWORK_ERROR"
        elif any(keyword in error_lower for keyword in ['rate limit', 'quota', 'exceeded']):
            return "RATE_LIMIT_ERROR"
        elif any(keyword in error_lower for keyword in ['function', 'parameter', 'argument']):
            return "FUNCTION_ERROR"
        elif any(keyword in error_lower for keyword in ['json', 'parse', 'format']):
            return "DATA_FORMAT_ERROR"
        else:
            return "UNKNOWN_ERROR"

    def get_diagnosis(self) -> str:
        """获取系统诊断"""
        if not self.error_log:
            return "系统运行正常，无错误记录"

        # 分析最近的错误
        recent_errors = self.error_log[-5:]  # 最近5个错误
        error_types = {}

        for error in recent_errors:
            error_type = error['error_type']
            error_types[error_type] = error_types.get(error_type, 0) + 1

        # 找出最常见的错误类型
        most_common_error = max(error_types.items(), key=lambda x: x[1]) if error_types else None

        diagnosis = f"最近错误: {self.error_log[-1]['error']}\n"

        if most_common_error:
            error_type, count = most_common_error
            diagnosis += f"主要错误类型: {error_type} (出现{count}次)\n"
            diagnosis += self._get_error_suggestions(error_type)

        return diagnosis

    def _get_error_suggestions(self, error_type: str) -> str:
        """根据错误类型提供建议"""
        suggestions = {
            "API_AUTH_ERROR": "建议检查API密钥配置和权限设置",
            "NETWORK_ERROR": "建议检查网络连接和防火墙设置",
            "RATE_LIMIT_ERROR": "建议降低请求频率或升级API套餐",
            "FUNCTION_ERROR": "建议检查函数参数格式和必需参数",
            "DATA_FORMAT_ERROR": "建议检查数据格式和JSON解析",
            "UNKNOWN_ERROR": "建议查看详细错误日志并联系技术支持"
        }
        return suggestions.get(error_type, "建议查看详细错误信息")

    def get_performance_report(self) -> Dict:
        """获取性能报告"""
        total = self.performance_metrics["successful_requests"] + self.performance_metrics["failed_requests"]
        success_rate = (self.performance_metrics["successful_requests"] / total * 100) if total > 0 else 0

        return {
            "total_requests": total,
            "success_rate": f"{success_rate:.1f}%",
            "successful_requests": self.performance_metrics["successful_requests"],
            "failed_requests": self.performance_metrics["failed_requests"],
            "average_response_time": f"{self.performance_metrics['average_response_time']:.2f}s",
            "recent_errors": len([e for e in self.error_log if
                                (datetime.now() - datetime.fromisoformat(e['timestamp'])).seconds < 300])
        }

    def get_fix_suggestions(self) -> List[str]:
        """获取修正建议"""
        if not self.error_log:
            return ["系统运行正常，无需修正"]

        suggestions = []

        # 分析错误模式
        error_types = {}
        for error in self.error_log[-10:]:  # 最近10个错误
            error_type = error['error_type']
            error_types[error_type] = error_types.get(error_type, 0) + 1

        # 根据错误类型提供具体建议
        for error_type, count in error_types.items():
            if count >= 3:  # 如果同类错误出现3次以上
                if error_type == "API_AUTH_ERROR":
                    suggestions.extend([
                        "检查环境变量 AMAP_API_KEY 和 BAILIAN_API_KEY 是否正确设置",
                        "验证API密钥是否有效且未过期",
                        "确认API服务权限配置正确"
                    ])
                elif error_type == "NETWORK_ERROR":
                    suggestions.extend([
                        "检查网络连接状态",
                        "验证防火墙和代理设置",
                        "考虑增加请求超时时间"
                    ])
                elif error_type == "RATE_LIMIT_ERROR":
                    suggestions.extend([
                        "实现请求频率限制",
                        "添加请求重试机制",
                        "考虑升级API套餐"
                    ])
                elif error_type == "FUNCTION_ERROR":
                    suggestions.extend([
                        "检查函数参数格式和类型",
                        "验证必需参数是否完整",
                        "确认函数定义正确"
                    ])

        # 检查重复错误
        recent_error_messages = [e['error'] for e in self.error_log[-5:]]
        if len(set(recent_error_messages)) == 1 and len(recent_error_messages) > 2:
            suggestions.append("检测到重复错误，建议检查根本原因")

        return suggestions if suggestions else ["查看详细错误日志以获取更多信息"]

    def log_function_debug(self, debug_info: dict):
        """记录Function调试信息"""
        entry = {
            "timestamp": datetime.now().isoformat(),
            **debug_info
        }
        self.function_debug_log.append(entry)

        # 保持日志大小合理
        if len(self.function_debug_log) > 100:
            self.function_debug_log = self.function_debug_log[-50:]

    def log_error(self,error_info, debug_info: dict):
        print(error_info)
        debug_info["error_info"]=error_info
        self.function_debug_log.append(debug_info)


    def get_function_debug_summary(self) -> dict:
        """获取Function调试摘要"""
        if not self.function_debug_log:
            return {"message": "暂无Function调试记录"}

        # 统计各种调试分数
        scores = [entry.get("debug_score", 1.0) for entry in self.function_debug_log]

        score_distribution = {
            "perfect": len([s for s in scores if s == 1.0]),
            "partial_success": len([s for s in scores if s == 0.5]),
            "parameter_issues": len([s for s in scores if s == 0]),
            "function_errors": len([s for s in scores if s == -1])
        }

        # 最近的问题
        recent_issues = []
        for entry in self.function_debug_log[-10:]:
            if entry.get("issues"):
                recent_issues.extend(entry["issues"])

        return {
            "total_function_calls": len(self.function_debug_log),
            "score_distribution": score_distribution,
            "recent_issues": recent_issues[-5:],  # 最近5个问题
            "success_rate": f"{(score_distribution['perfect'] / len(scores) * 100):.1f}%" if scores else "0%"
        }

class EnhancedTaxiAgent:
    """增强版打车Agent，集成高德地图和打车服务"""
    def __init__(self):
        self.state = StateManager()
        self.tool_agent = ToolAgent(self.state)
        self.completion_agent = CompletionAgent(self.state)
        self.debug_agent = DebugAgent(self.state)

        # 初始化参数管理器
        try:
            amap_tools = AmapMCPTools()
            self.parameter_manager = FunctionParameterManager(amap_tools)
        except Exception as e:
            print(f"警告: 参数管理器初始化失败: {e}")
            self.parameter_manager = FunctionParameterManager()

        # 任务管理系统
        self.pending_tasks = {}  # 待确认任务列表: {task_id: TaskInfo}
        self.executed_tasks = {}  # 已执行任务列表: {task_key: TaskInfo}
        self.task_counter = 0  # 任务计数器

        # 位置上下文信息
        self.location_context = {
            "default_longitude": DEFAULT_LONGITUDE,
            "default_latitude": DEFAULT_LATITUDE,
            "current_location_info": None,
            "last_updated": None
        }

        # 百炼模型配置
        api_key = os.getenv("BAILIAN_API_KEY")
        if api_key:
            self.bailian_client = OpenAI(
                api_key=api_key,
                base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
            )
            self.bailian_model_name = "qwen-max"
        else:
            print("警告: 未设置BAILIAN_API_KEY环境变量，AI对话功能将不可用")
            self.bailian_client = None
            self.bailian_model_name = None

        # 初始化高德地图工具
        try:
            self.amap_tools = AmapMCPTools()
        except Exception as e:
            print(f"警告: 高德地图工具初始化失败: {e}")
            self.amap_tools = None

        # 系统提示词 - 优化为更自然的对话风格
        self.system_prompt = """你是一个智能打车助手，说话要自然、简洁、口语化，像真人一样交流。

核心原则：
1. 回复要简短有力，避免重复啰嗦
2. 像朋友聊天一样自然，不要太正式
3. 主动利用环境信息补全缺失信息
4. 发现冲突时要及时提醒用户

对话策略：
- 用户说"打车"时，直接问"去哪？"
- 用户说地点时，如果模糊就帮忙确认具体位置
- 有了起点终点后，主动提供时间、费用预估
- 发现时间冲突时要提醒用户

环境感知：
- 利用当前位置信息作为默认起点
- 根据当前时间判断是否合理
- 检测用户需求与实际情况的冲突"""

        self.context = {}  # 存储对话上下文
        self.action_state_hist = []  # 动作状态历史记录
        # 定义可用的工具
        self.tools = [
            # 高德地图工具
            {
                "type": "function",
                "function": {
                    "name": "mcp_geocode_address",
                    "description": "将地点名称转换为经纬度坐标。支持地址、景点、建筑物等各种地点名称。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "address": {
                                "type": "string",
                                "description": "地点名称或地址，如'西湖'、'杭州东站'、'北京天安门'"
                            },
                            "city": {
                                "type": "string",
                                "description": "城市名称，可选，用于提高搜索精度，如'杭州'、'北京'"
                            }
                        },
                        "required": ["address"]
                    }
                }
            },
            # {
            #     "type": "function",
            #     "function": {
            #         "name": "mcp_search_poi",
            #         "description": "搜索POI（兴趣点），如餐厅、酒店、景点等。",
            #         "parameters": {
            #             "type": "object",
            #             "properties": {
            #                 "keyword": {
            #                     "type": "string",
            #                     "description": "搜索关键词，如'星巴克'、'酒店'、'加油站'"
            #                 },
            #                 "city": {
            #                     "type": "string",
            #                     "description": "城市名称，可选，用于限定搜索范围"
            #                 },
            #                 "types": {
            #                     "type": "string",
            #                     "description": "POI类型，可选，如'餐饮服务'、'住宿服务'"
            #                 }
            #             },
            #             "required": ["keyword"]
            #         }
            #     }
            # },
            # {
            #     "type": "function",
            #     "function": {
            #         "name": "mcp_reverse_geocode_poi",
            #         "description": "根据经纬度坐标查找附近的POI（兴趣点）。输入经纬度，输出附近的商店、餐厅、景点等。",
            #         "parameters": {
            #             "type": "object",
            #             "properties": {
            #                 "longitude": {
            #                     "type": "number",
            #                     "description": "经度，如116.397428"
            #                 },
            #                 "latitude": {
            #                     "type": "number",
            #                     "description": "纬度，如39.90923"
            #                 },
            #                 "radius": {
            #                     "type": "integer",
            #                     "description": "搜索半径（米），默认1000米，最大3000米"
            #                 }
            #             },
            #             "required": ["longitude", "latitude"]
            #         }
            #     }
            # },
            {
                "type": "function",
                "function": {
                    "name": "mcp_recommend_similar_poi",
                    "description": "根据POI名称推荐附近相似的POI。例如输入'北京上地星巴克'，会推荐上地星巴克附近的咖啡店，而不是北京所有的星巴克。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "poi_name": {
                                "type": "string",
                                "description": "POI名称，可以是模糊名称，如'北京上地星巴克'、'杭州西湖银泰'，环境范围越具体越好"
                            },
                            "city": {
                                "type": "string",
                                "description": "城市名称，可选，用于提高搜索精度"
                            },
                            "radius": {
                                "type": "integer",
                                "description": "推荐范围半径（米），默认2000米"
                            }
                        },
                        "required": ["poi_name"]
                    }
                }
            },
            # 导航距离和时间计算工具
            {
                "type": "function",
                "function": {
                    "name": "mcp_calculate_driving_route",
                    "description": "计算两个经纬度坐标之间的驾车导航距离和预估时间。返回距离、时间、过路费、红绿灯数量等信息。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "origin_lng": {
                                "type": "number",
                                "description": "起点经度，如116.397428"
                            },
                            "origin_lat": {
                                "type": "number",
                                "description": "起点纬度，如39.90923"
                            },
                            "dest_lng": {
                                "type": "number",
                                "description": "终点经度，如116.465302"
                            },
                            "dest_lat": {
                                "type": "number",
                                "description": "终点纬度，如40.004717"
                            },
                            "strategy": {
                                "type": "integer",
                                "description": "路径规划策略，默认10（躲避拥堵，路程较短）。可选值：10-20为多策略，0-9为单策略"
                            }
                        },
                        "required": ["origin_lng", "origin_lat", "dest_lng", "dest_lat"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "mcp_calculate_poi_to_poi_route",
                    "description": "计算两个POI名称之间的驾车导航距离和预估时间。先将POI名称转换为坐标，再计算路径。适用于'从北京天安门到北京西站'这类查询。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "origin_poi": {
                                "type": "string",
                                "description": "起点POI名称，如'北京天安门'、'杭州西湖'"
                            },
                            "dest_poi": {
                                "type": "string",
                                "description": "终点POI名称，如'北京西站'、'杭州东站'"
                            },
                            "origin_city": {
                                "type": "string",
                                "description": "起点城市名称，可选，用于提高搜索精度"
                            },
                            "dest_city": {
                                "type": "string",
                                "description": "终点城市名称，可选，用于提高搜索精度"
                            },
                            "strategy": {
                                "type": "integer",
                                "description": "路径规划策略，默认10（躲避拥堵，路程较短）"
                            }
                        },
                        "required": ["origin_poi", "dest_poi"]
                    }
                }
            },
            # 打车服务工具
            {
                "type": "function",
                "function": {
                    "name": "call_taxi_service",
                    "description": "调用打车服务，用户存在打车的意图，为用户安排车辆从起点到终点。比如用户询问打车，我要打车或者隐含打车的意图, 如果不存在出发地,可以使用当前所在位置进行补全",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "start_place": {
                                "type": "string",
                                "description": "出发地点名称，必须提供,如果不存在出发地,可以使用用户当前所在位置进行补全"
                            },
                            "end_place": {
                                "type": "string",
                                "description": "目的地名称，必须提供"
                            },
                            "car_prefer": {
                                "type": "string",
                                "description": "车辆偏好，可选，如'经济型'、'舒适型'、'豪华型'等"
                            }
                        },
                        "required": ["start_place", "end_place"]
                    }
                }
            },
            # 上车点推荐工具
            # {
            #     "type": "function",
            #     "function": {
            #         "name": "mcp_search_taxi_spots",
            #         "description": "搜索上车点推荐。根据指定位置搜索附近适合打车的上车点，如地铁站、酒店、商场等交通便利的地点。",
            #         "parameters": {
            #             "type": "object",
            #             "properties": {
            #                 "location": {
            #                     "type": "string",
            #                     "description": "位置名称或地址，如'北京大学'、'方正大厦'、'海淀医院'"
            #                 },
            #                 "city": {
            #                     "type": "string",
            #                     "description": "城市名称，可选，用于提高搜索精度"
            #                 },
            #                 "radius": {
            #                     "type": "integer",
            #                     "description": "搜索半径（米），默认1000米，建议500-2000米"
            #                 }
            #             },
            #             "required": ["location"]
            #         }
            #     }
            # },
            # 打车价格估算工具
            {
                "type": "function",
                "function": {
                    "name": "mcp_estimate_taxi_price",
                    "description": "估算打车价格。基于起点终点距离和时间，估算不同车型的打车费用，包括起步价、里程费、时长费等。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "origin_poi": {
                                "type": "string",
                                "description": "起点POI名称，如'北京天安门'、'杭州西湖'"
                            },
                            "dest_poi": {
                                "type": "string",
                                "description": "终点POI名称，如'北京西站'、'杭州东站'"
                            },
                            "origin_city": {
                                "type": "string",
                                "description": "起点城市名称，可选，用于提高搜索精度"
                            },
                            "dest_city": {
                                "type": "string",
                                "description": "终点城市名称，可选，用于提高搜索精度"
                            },
                            "car_type": {
                                "type": "string",
                                "description": "车型类型，可选值：'经济型'、'舒适型'、'豪华型'，默认'经济型'"
                            }
                        },
                        "required": ["origin_poi", "dest_poi"]
                    }
                }
            },
            # 参数确认工具
            {
                "type": "function",
                "function": {
                    "name": "confirm_parameter",
                    "description": "确认低容错率函数的参数。用于用户确认已验证的参数值。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "function_name": {
                                "type": "string",
                                "description": "函数名称，如'call_taxi_service'"
                            },
                            "parameter_name": {
                                "type": "string",
                                "description": "参数名称，如'start_place'、'end_place'"
                            },
                            "parameter_value": {
                                "type": "string",
                                "description": "参数值，用于确认"
                            }
                        },
                        "required": ["function_name", "parameter_name", "parameter_value"]
                    }
                }
            },
            # 参数取消工具
            {
                "type": "function",
                "function": {
                    "name": "cancel_parameter",
                    "description": "取消/重置低容错率函数的参数。用于用户取消或重新设置参数值。",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "function_name": {
                                "type": "string",
                                "description": "函数名称，如'call_taxi_service'"
                            },
                            "parameter_name": {
                                "type": "string",
                                "description": "参数名称，如'start_place'、'end_place'"
                            }
                        },
                        "required": ["function_name", "parameter_name"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "confirm_important_task",
                    "description": "用户确认低容错率函数的任务，可以执行获取相关信息",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "function_name": {
                                "type": "string",
                                "description": "函数名称，如'call_taxi_service'"
                            }
                        },
                        "required": ["function_name"]
                    }
                }
            },
            # 任务管理工具
            {
                "type": "function",
                "function": {
                    "name": "confirm_parameter",
                    "description": "确认任务参数，将参数状态置为'已确认'",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "task_id": {
                                "type": "string",
                                "description": "任务ID"
                            },
                            "param_name": {
                                "type": "string",
                                "description": "参数名称"
                            }
                        },
                        "required": ["task_id", "param_name"]
                    }
                }
            },
            {
                "type": "function",
                "function": {
                    "name": "cancel_parameter",
                    "description": "取消任务参数，将参数状态置为'未填写'",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "task_id": {
                                "type": "string",
                                "description": "任务ID"
                            },
                            "param_name": {
                                "type": "string",
                                "description": "参数名称"
                            }
                        },
                        "required": ["task_id", "param_name"]
                    }
                }
            }
        ]

        # self.tools_args_check = ["type": "function",
        #         "function": {
        #             "name": "call_taxi_service",
        #             "description": "调用打车服务，为用户安排车辆从起点到终点。",
        #             "parameters": {
        #                 "type": "object",
        #                 "properties": {
        #                     "start_place": {
        #                         "type": "string",
        #                         "description": "出发地点名称，必须提供"
        #                     },
        #                     "end_place": {
        #                         "type": "string",
        #                         "description": "目的地名称，必须提供"
        #                     },
        #                     "car_prefer": {
        #                         "type": "string",
        #                         "description": "车辆偏好，可选，如'经济型'、'舒适型'、'豪华型'等"
        #                     }
        #                 },
        #                 "required": ["start_place", "end_place"]
        #             }
        #         }
        #     },]



        # 初始化位置上下文
        if LOCATION_CONTEXT_ENABLED:
            self._initialize_location_context()

        self.tools_extend_feature = {"call_taxi_service":
            {"name": "call_taxi_service",
             "type":"function",
             "backup_format_description":"打车从北京大学到海淀区方正大厦，经济型",
             "low_fault_tolerance":"低"
             },"mcp_geocode_address":
            {"name": "mcp_geocode_address",
             "type":"function",
             "backup_format_description":"杭州西湖的经纬度坐标",
             "low_fault_tolerance":"高"
             },
             "mcp_get_city_code":{"name": "mcp_get_city_code",
              "type":"function",
              "backup_format_description":"杭州市的adcode",
              "low_fault_tolerance":"高"
              },
             "mcp_search_poi":{"name": "mcp_search_poi",
              "type":"function",
              "backup_format_description":"海淀区的星巴克",
              "low_fault_tolerance":"高"
              },
             "mcp_reverse_geocode_poi":{"name": "mcp_reverse_geocode_poi",
              "type":"function",
              "backup_format_description":"经纬度116.397428,39.90923附近的POI",
              "low_fault_tolerance":"高"
              },
             "mcp_recommend_similar_poi":{"name": "mcp_recommend_similar_poi",
              "type":"function",
              "backup_format_description":"北京上地星巴克附近的相似咖啡店",
              "low_fault_tolerance":"高"
              },
             "mcp_calculate_driving_route":{"name": "mcp_calculate_driving_route",
              "type":"function",
              "backup_format_description":"从经纬度116.397428,39.90923到116.465302,40.004717的驾车距离和时间",
              "low_fault_tolerance":"高"
              },
             "mcp_calculate_poi_to_poi_route":{"name": "mcp_calculate_poi_to_poi_route",
              "type":"function",
              "backup_format_description":"从北京天安门到北京西站的驾车距离和时间",
              "low_fault_tolerance":"高"
              },
             "mcp_search_taxi_spots":{"name": "mcp_search_taxi_spots",
              "type":"function",
              "backup_format_description":"北京大学附近的上车点推荐",
              "low_fault_tolerance":"高"
              },
             "mcp_estimate_taxi_price":{"name": "mcp_estimate_taxi_price",
              "type":"function",
              "backup_format_description":"从北京天安门到北京西站的打车价格估算",
              "low_fault_tolerance":"低"
              },
             "confirm_parameter":{"name": "confirm_parameter",
              "type":"function",
              "backup_format_description":"确认打车起点参数",
              "low_fault_tolerance":"高"
              },
             "cancel_parameter":{"name": "cancel_parameter",
              "type":"function",
              "backup_format_description":"取消打车起点参数",
              "low_fault_tolerance":"高"
              }
        }

    def _initialize_location_context(self):
        """初始化位置上下文信息"""
        try:
            print(f"正在初始化位置上下文，默认坐标: {DEFAULT_LONGITUDE}, {DEFAULT_LATITUDE}")

            # 使用默认经纬度获取位置信息
            location_result = mcp_reverse_geocode_poi(
                longitude=DEFAULT_LONGITUDE,
                latitude=DEFAULT_LATITUDE,
                radius=1000
            )
            print("location_result:",location_result)

            if location_result.get("status"):
                data = location_result.get("data", {})
                self.location_context["current_location_info"] = {
                    "formatted_address": data.get("formatted_address", ""),
                    "nearby_pois": data.get("nearby_pois", [])[:3],  # 取前3个POI
                    "coordinates": {
                        "longitude": DEFAULT_LONGITUDE,
                        "latitude": DEFAULT_LATITUDE
                    }
                }
                print("self.location_context[\"current_location_info\"]",self.location_context["current_location_info"])
                self.location_context["last_updated"] = datetime.now().isoformat()

                # 更新系统提示词，加入位置上下文
                self._update_system_prompt_with_location()

                print("✅ 位置上下文初始化成功")
            else:
                print(f"⚠️ 位置上下文初始化失败: {location_result.get('error', '未知错误')}")

        except Exception as e:
            print(f"❌ 位置上下文初始化出错: {e}")

    def _update_system_prompt_with_location(self):
        """更新系统提示词，加入位置上下文信息"""
        location_info = self.location_context.get("current_location_info")
        print("location_info.get('formatted_address', '未知位置')",location_info.get('formatted_address', '未知位置'))
        if not location_info:
            return

        # 构建位置上下文描述
        location_context_text = f"""
- 用户当前所在位置：{location_info.get('formatted_address', '未知位置')}
- 坐标：经度{self.location_context['default_longitude']}, 纬度{self.location_context['default_latitude']}"""

        # # 添加附近POI信息
        # nearby_pois = location_info.get("nearby_pois", [])
        # if nearby_pois:
        #     poi_list = []
        #     for poi in nearby_pois:
        #         poi_name = poi.get("name", "")
        #         poi_type = poi.get("type", "")
        #         distance = poi.get("distance", 0)
        #         if poi_name:
        #             poi_list.append(f"{poi_name}({poi_type}, {distance}米)")

        #     if poi_list:
        #         location_context_text += f"\n- 附近地标：{', '.join(poi_list)}"


        # 获取当前时间信息
        now = datetime.now()
        current_time = now.strftime("%H:%M")
        current_hour = now.hour

        # 根据时间判断时段
        if 6 <= current_hour < 12:
            time_period = "上午"
        elif 12 <= current_hour < 18:
            time_period = "下午"
        elif 18 <= current_hour < 22:
            time_period = "晚上"
        else:
            time_period = "深夜"

        # 更新系统提示词 - 包含智能环境感知
        self.system_prompt = f"""你是一个智能助手，说话要自然、简洁、口语化，像真人一样交流。

核心原则：
1. 回复要简短有力，避免重复啰嗦
2. 像朋友聊天一样自然，不要太正式
3. 主动利用环境信息补全缺失信息
4. 发现冲突时要及时提醒用户
5. 根据上下文信息确定最后一轮的调用具体工具，查询、确认、取消相关的变量或者任务；**如果没有相关入参信息或者未知的入参信息，也必须发起function calling，参数值都用"无"代替先占位**
6. 必须结合历史对话思考、补全、理解用户最后一轮的对话的意图和需要调用的工具，比如最后一句可能与之前相同意图，只是修改了相关的入参信息，也有可能是入参信息是相同的，但是意图发生了变化，这个很重要
7. 当用户提供的地理位置信息存在歧义或错误，请根据工具参数以及相关结果（多个类似候选的参数），引导用户提供更准确的位置信息。如果存在多个候选的入参，询问用户是哪个候选结果

当前环境信息：
{location_context_text}
现在是{time_period} {current_time}


在处理用户请求时，要充分利用当前位置和时间信息，提供更智能的服务。"""
        print("self.system_prompt:",self.system_prompt)
        print(f"✅ 系统提示词已更新，包含位置上下文")

    def _preprocess_user_input(self, user_input: str, session_id: str) -> str:
        """预处理用户输入，添加环境信息和冲突检测"""
        processed_input = user_input

        # 检测打车意图并补全环境信息
        if self._is_taxi_request(user_input):
            # 添加当前位置信息
            if self.location_context:
                location_info = f"\n[环境信息：用户当前位置在{self.location_context.get('address', '未知位置')}]"
                processed_input += location_info

            # 添加时间信息和冲突检测
            time_analysis = self._analyze_time_conflicts(user_input)
            if time_analysis:
                processed_input += f"\n[时间分析：{time_analysis}]"

        return processed_input

    def _is_taxi_request(self, user_input: str) -> bool:
        """判断是否为打车相关请求"""
        taxi_keywords = ["打车", "叫车", "出租车", "去", "到", "机场", "车站", "从"]
        return any(keyword in user_input for keyword in taxi_keywords)

    def _analyze_time_conflicts(self, user_input: str) -> str:
        """分析时间冲突"""
        from datetime import datetime
        import re

        now = datetime.now()
        current_hour = now.hour

        # 检测用户提到的时间
        time_patterns = [
            r"(\d{1,2})点",
            r"(\d{1,2}):(\d{2})",
            r"晚上(\d{1,2})点",
            r"今天晚上(\d{1,2})点"
        ]

        mentioned_times = []
        for pattern in time_patterns:
            matches = re.findall(pattern, user_input)
            for match in matches:
                if isinstance(match, tuple):
                    if len(match) == 2:  # HH:MM格式
                        hour = int(match[0])
                        mentioned_times.append(hour)
                    else:  # 晚上X点格式
                        hour = int(match[0])
                        if hour < 12:
                            hour += 12  # 转换为24小时制
                        mentioned_times.append(hour)
                else:  # X点格式
                    hour = int(match)
                    mentioned_times.append(hour)

        # 分析时间冲突
        if mentioned_times:
            target_hour = mentioned_times[0]
            time_diff = target_hour - current_hour

            # 检测是否有明显的时间冲突
            if "飞机" in user_input or "航班" in user_input:
                if time_diff > 3:
                    return f"现在{current_hour}点，{target_hour}点的飞机，现在出发可能太早"
                elif time_diff < 1:
                    return f"现在{current_hour}点，{target_hour}点的飞机，时间可能比较紧张"

            if time_diff > 4:
                return f"现在{current_hour}点，{target_hour}点出发，是否需要预约？"

        return ""

    def get_location_context(self) -> dict:
        """获取当前位置上下文信息"""
        return self.location_context.copy()
    

    def update_location_context(self, longitude: float, latitude: float):
        """更新位置上下文"""
        try:
            # 获取新位置的信息
            location_result = mcp_reverse_geocode_poi(
                longitude=longitude,
                latitude=latitude,
                radius=1000
            )

            if location_result.get("status"):
                data = location_result.get("data", {})
                self.location_context["current_location_info"] = {
                    "formatted_address": data.get("formatted_address", ""),
                    "nearby_pois": data.get("nearby_pois", [])[:3],
                    "coordinates": {
                        "longitude": longitude,
                        "latitude": latitude
                    }
                }
                self.location_context["default_longitude"] = longitude
                self.location_context["default_latitude"] = latitude
                self.location_context["last_updated"] = datetime.now().isoformat()

                # 更新系统提示词
                self._update_system_prompt_with_location()

                return {"status": True, "message": "位置上下文更新成功"}
            else:
                return {"status": False, "error": location_result.get("error", "位置信息获取失败")}

        except Exception as e:
            return {"status": False, "error": f"更新位置上下文失败: {str(e)}"}

    def process_message(self, user_input: str, session_id: str = "default") -> str:
        """处理用户消息的主要方法 - 优化版"""
        try:
            # 记录开始状态
            self.state.log_execution(
                action="process_message",
                status="processing",
                details={"user_input": user_input, "session_id": session_id}
            )

            # 检查是否有可用的AI客户端
            if not self.bailian_client:
                return "抱歉，AI对话功能暂不可用。请设置BAILIAN_API_KEY环境变量。"

            # 预处理：智能意图识别和环境信息补全
            # processed_input = self._preprocess_user_input(user_input, session_id)

            # 初始化或更新对话上下文
            if session_id not in self.context:
                self.context[session_id] = [
                    {"role": "system", "content": self.system_prompt}
                ]

            # 添加处理后的用户消息
            self.context[session_id].append({"role": "user", "content": user_input})
            # print()
            # 调用百炼API获取响应
            response = self.bailian_client.chat.completions.create(
                model="qwen-max",
                # self.bailian_model_name,
                messages=self.context[session_id],
                tools=self.tools,
                # tool_choice="auto",
                parallel_tool_calls=True
            )

            response_message = response.choices[0].message
            self.context[session_id].append(response_message)
            print("response_message:",response_message)
            # 检查是否需要function calling
            if response_message.tool_calls:
                # 处理每个function call
                print("response_message.tool_calls:",response_message.tool_calls)
                for tool_call in response_message.tool_calls:
                    function_name = tool_call.function.name
                    function_args = json.loads(tool_call.function.arguments)
        
                    # 打印function calling详细信息
                    self._print_function_call_details(function_name, function_args, session_id)

                    # 处理特殊的确认和取消函数
                    if function_name == "confirm_parameter":
                        function_response = self._handle_confirm_parameter(function_args, session_id)
                        # 检查是否有新的可执行任务
                        self._check_and_execute_ready_tasks(session_id)
                    elif function_name == "cancel_parameter":
                        function_response = self._handle_cancel_parameter(function_args, session_id)
                    else:
                        # 创建或更新任务
                        task_id = self._handle_function_call_with_task_management(
                            function_name, function_args, session_id
                        )

                        # 检查任务是否可执行
                        executable_check = self.check_task_executable(task_id)

                        if executable_check["can_execute"]:
                            # 执行任务
                            execution_result = self.execute_task(task_id)
                            function_response = execution_result.get("result", execution_result)
                        else:
                            # 任务不可执行，返回需要的操作
                            function_response = {
                                "status": False,
                                "task_id": task_id,
                                "executable_check": executable_check,
                                "need_user_action": True
                            }

                    # 记录到历史
                    self.action_state_hist.append({
                        "function_name": function_name,
                        "function_args": function_args,
                        "function_response": function_response,
                        "can_execute": function_response.get("status", False)
                    })
                    # 打印function response详细信息
                    self._print_function_response_details(function_name, function_response, {})

                    # 将结果添加到上下文，包含候选信息和确认提示
                    fault_tolerance_id = self.tools_extend_feature.get(function_name, {}).get("low_fault_tolerance", "中")
                    actionable_info = ""
                    # f"\n该函数的容错性是{fault_tolerance_id}"

                    # 如果需要确认且有候选信息，添加到prompt中
                    if not function_response.get("status", True):
                        candidate_info = function_response.get("candidate_info", {})
                        print("candidate_info:",candidate_info)
                        if candidate_info:
                            for item in candidate_info.values():
                                actionable_info += str(item)
                    print("actionable_info:",actionable_info)
                    self.context[session_id].append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "name": function_name,
                        "content": json.dumps(function_response, ensure_ascii=False) + actionable_info
                    })

                # 根据验证结果生成不同的系统提示
                # system_prompt = self._generate_system_prompt_for_response(validation_result)
                # print("system_prompt:",system_prompt)
                
                # 获取最终回复并优化简洁性
                final_response = self.bailian_client.chat.completions.create(
                    model=self.bailian_model_name,
                    messages=self.context[session_id] 
                    # + [
                    #     {"role": "system", "content": system_prompt}
                    # ]
                )
                print("mrssage:",self.context[session_id])
                final_message = final_response.choices[0].message.content
                print("final_message:",final_message)
                # 进一步优化回复简洁性
                final_message = self._optimize_response_brevity(final_message)

                self.context[session_id].append({
                    "role": "assistant",
                    "content": final_message
                })

                # 记录成功状态
                self.state.log_execution(
                    action="process_message",
                    status="success",
                    details={"response": final_message}
                )

                return final_message
            else:
                # 直接返回响应
                final_message = response_message.content
                self.context[session_id].append({
                    "role": "assistant",
                    "content": final_message
                })

                # 记录成功状态
                self.state.log_execution(
                    action="process_message",
                    status="success",
                    details={"response": final_message}
                )

                return final_message

        except Exception as e:
            error_msg = f"处理请求时出错: {str(e)}"
            self.debug_agent.log_error(str(e), {"input": user_input, "session_id": session_id})
            self.state.log_execution(
                action="process_message",
                status="failed",
                details={"error": str(e)}
            )
            return error_msg

    def _print_function_call_details(self, function_name: str, function_args: dict, session_id: str):
        """打印function calling的详细信息"""
        print("\n" + "="*80)
        print(f"🔧 Function Call 详细信息")
        print("="*80)
        print(f"📋 函数名: {function_name}")
        print(f"📊 入参: {json.dumps(function_args, ensure_ascii=False, indent=2)}")
        print(f"🆔 会话ID: {session_id}")

        # 获取函数容错率信息
        fault_tolerance = self.tools_extend_feature.get(function_name, {}).get("low_fault_tolerance", "未知")
        print(f"🔧 容错率: {fault_tolerance}")

        # 分析参数状态
        try:
            if hasattr(self, 'parameter_manager') and self.parameter_manager:
                # 初始化参数以获取状态
                init_result = self.parameter_manager.initialize_function_parameters(function_name, function_args)

                if init_result.get("status"):
                    print(f"\n📝 参数分析:")
                    print(f"   必填参数: {init_result.get('required_params', [])}")
                    print(f"   可选参数: {init_result.get('optional_params', [])}")
                    print(f"   已提供参数: {init_result.get('provided_params', [])}")
                    print(f"   缺少必填参数: {init_result.get('missing_required', [])}")

                    # 验证参数
                    validation_result = self.parameter_manager.validate_all_parameters(function_name)
                    if validation_result.get("status"):
                        print(f"   验证结果: {'✅ 全部有效' if validation_result.get('all_valid') else '❌ 存在无效参数'}")

                        # 显示每个参数的详细状态
                        status = self.parameter_manager.get_function_status(function_name)
                        if status.get("status"):
                            parameters = status.get("parameters", {})
                            print(f"\n📊 参数状态详情:")
                            for param_name, param_info in parameters.items():
                                state = param_info.get("state", "未知")
                                value = param_info.get("value", "无")
                                confusion_score = param_info.get("confusion_score")

                                state_icons = {
                                    "未填写": "⚪",
                                    "未校验": "🟡",
                                    "已校验待确认": "🟠",
                                    "已确认": "🟢"
                                }
                                icon = state_icons.get(state, "❓")

                                print(f"   {icon} {param_name}: {state} (值: {value})")
                                if confusion_score is not None:
                                    level = "低" if confusion_score <= 0.2 else "中" if confusion_score <= 0.5 else "高"
                                    print(f"      困惑度: {confusion_score:.2f} ({level})")

                    # 检查执行条件
                    execution_check = self.parameter_manager.can_execute_function(function_name)
                    can_execute = execution_check.get("can_execute", False)

                    print(f"\n🚀 执行状态: {'✅ 可以执行' if can_execute else '❌ 不能执行'}")

                    if not can_execute:
                        missing_required = execution_check.get("missing_required", [])
                        unvalidated_params = execution_check.get("unvalidated_params", [])
                        unconfirmed_params = execution_check.get("unconfirmed_params", [])

                        print(f"📋 执行阻塞原因:")
                        if missing_required:
                            print(f"   ⚠️  缺少必填参数: {missing_required}")
                        if unvalidated_params:
                            print(f"   ⚠️  待校验参数: {unvalidated_params}")
                        if unconfirmed_params:
                            print(f"   ⚠️  待确认参数: {unconfirmed_params}")
                            if fault_tolerance == "低":
                                print(f"   💡 低容错率函数需要用户确认所有参数")

                    # 如果是待确认状态，提供具体方案
                    if not can_execute and execution_check.get("unconfirmed_params"):
                        self._print_confirmation_proposal(function_name, function_args, execution_check)

        except Exception as e:
            print(f"❌ 参数分析出错: {str(e)}")

        print("="*80)

    def _print_confirmation_proposal(self, function_name: str, function_args: dict, execution_check: dict):
        """打印待确认的具体方案和可选项"""
        print(f"\n💡 待确认方案:")

        if function_name == "call_taxi_service":
            start_place = function_args.get("start_place", "未知起点")
            end_place = function_args.get("end_place", "未知终点")
            car_prefer = function_args.get("car_prefer", "经济型")

            print(f"   🚗 打车方案:")
            print(f"      起点: {start_place}")
            print(f"      终点: {end_place}")
            print(f"      车型偏好: {car_prefer}")

            # 尝试获取路线和价格信息
            try:
                route_result = mcp_calculate_poi_to_poi_route(start_place, end_place)
                if route_result.get("status"):
                    data = route_result.get("data", {})
                    distance = data.get("distance", 0) / 1000  # 转换为公里
                    duration = data.get("duration", 0) / 60    # 转换为分钟
                    tolls = data.get("tolls", 0)

                    print(f"      预计距离: {distance:.1f}公里")
                    print(f"      预计时间: {duration:.0f}分钟")
                    if tolls > 0:
                        print(f"      过路费: {tolls}元")

                # 尝试获取价格估算
                price_result = mcp_estimate_taxi_price(start_place, end_place, car_type=car_prefer)
                if price_result.get("status"):
                    price_data = price_result.get("data", {})
                    estimated_price = price_data.get("estimated_price", "未知")
                    print(f"      预计费用: {estimated_price}元")

            except Exception as e:
                print(f"      ⚠️  无法获取详细路线信息: {str(e)}")

            print(f"\n🔄 可选操作:")
            print(f"   1. 确认当前方案")
            print(f"   2. 修改起点")
            print(f"   3. 修改终点")
            print(f"   4. 更换车型 (经济型/舒适型/豪华型)")
            print(f"   5. 取消打车")

        elif function_name == "mcp_estimate_taxi_price":
            origin_poi = function_args.get("origin_poi", "未知起点")
            dest_poi = function_args.get("dest_poi", "未知终点")
            car_type = function_args.get("car_type", "经济型")

            print(f"   💰 价格估算方案:")
            print(f"      起点: {origin_poi}")
            print(f"      终点: {dest_poi}")
            print(f"      车型: {car_type}")

            print(f"\n🔄 可选操作:")
            print(f"   1. 确认估算参数")
            print(f"   2. 修改起点")
            print(f"   3. 修改终点")
            print(f"   4. 更换车型")
            print(f"   5. 取消估算")

    def _print_function_response_details(self, function_name: str, function_response: dict, validation_result: dict):
        """打印function response的详细信息"""
        print("\n" + "="*80)
        print(f"📤 Function Response 详细信息")
        print("="*80)
        print(f"📋 函数名: {function_name}")

        # 分析validation_result
        can_execute = validation_result.get("can_execute", False)
        need_user_action = validation_result.get("need_user_action", False)
        action_type = validation_result.get("action_type", "")

        print(f"🚀 执行状态: {'✅ 已执行' if can_execute else '❌ 未执行'}")

        if not can_execute:
            print(f"📋 未执行原因:")
            if validation_result.get("error"):
                print(f"   ❌ 错误: {validation_result.get('error')}")

            if need_user_action:
                print(f"   🔄 需要用户操作: {action_type}")

                if action_type == "provide_parameters":
                    missing_params = validation_result.get("missing_parameters", [])
                    print(f"   📝 缺少参数: {missing_params}")
                    print(f"   💡 建议: 请提供缺少的参数信息")

                elif action_type == "fix_parameters":
                    validation_errors = validation_result.get("validation_errors", [])
                    print(f"   ⚠️  参数错误: {validation_errors}")
                    print(f"   💡 建议: 请修正参数或提供更具体的信息")

                elif action_type == "confirm_parameters":
                    unconfirmed_params = validation_result.get("unconfirmed_parameters", [])
                    function_name_ref = validation_result.get("function_name", function_name)
                    print(f"   ⏳ 待确认参数: {unconfirmed_params}")
                    print(f"   💡 建议: 请确认 {function_name_ref} 的参数")

                    # 显示当前参数状态
                    try:
                        if hasattr(self, 'parameter_manager') and self.parameter_manager:
                            status = self.parameter_manager.get_function_status(function_name_ref)
                            if status.get("status"):
                                parameters = status.get("parameters", {})
                                print(f"   📊 当前参数状态:")
                                for param_name in unconfirmed_params:
                                    if param_name in parameters:
                                        param_info = parameters[param_name]
                                        value = param_info.get("value", "无")
                                        confusion_score = param_info.get("confusion_score")
                                        print(f"      🟠 {param_name}: {value}")
                                        if confusion_score is not None:
                                            level = "低" if confusion_score <= 0.2 else "中" if confusion_score <= 0.5 else "高"
                                            print(f"         困惑度: {confusion_score:.2f} ({level})")
                    except Exception as e:
                        print(f"   ❌ 获取参数状态失败: {str(e)}")
        else:
            # 函数已执行，显示执行结果
            print(f"📤 执行结果:")
            if isinstance(function_response, dict):
                if function_response.get("status"):
                    print(f"   ✅ 执行成功")
                    data = function_response.get("data", {})

                    # 根据不同函数类型显示相关信息
                    if function_name == "mcp_calculate_poi_to_poi_route":
                        distance = data.get("distance", 0) / 1000
                        duration = data.get("duration", 0) / 60
                        tolls = data.get("tolls", 0)
                        origin_poi = data.get("origin_poi", "未知")
                        destination_poi = data.get("destination_poi", "未知")

                        print(f"   🗺️  路线信息:")
                        print(f"      起点: {origin_poi}")
                        print(f"      终点: {destination_poi}")
                        print(f"      距离: {distance:.1f}公里")
                        print(f"      时间: {duration:.0f}分钟")
                        if tolls > 0:
                            print(f"      过路费: {tolls}元")

                    elif function_name == "mcp_estimate_taxi_price":
                        estimated_price = data.get("estimated_price", "未知")
                        car_type = data.get("car_type", "经济型")
                        print(f"   💰 价格信息:")
                        print(f"      车型: {car_type}")
                        print(f"      预计费用: {estimated_price}元")

                    elif function_name == "mcp_search_poi":
                        pois = data.get("pois", [])
                        confusion_score = data.get("confusion_score", 0.0)
                        print(f"   🔍 搜索结果:")
                        print(f"      找到POI数量: {len(pois)}")
                        print(f"      困惑度: {confusion_score:.2f}")
                        if pois:
                            print(f"      前3个结果:")
                            for i, poi in enumerate(pois[:3], 1):
                                name = poi.get("name", "未知")
                                address = poi.get("address", "未知地址")
                                print(f"        {i}. {name} - {address}")

                    elif function_name == "call_taxi_service":
                        print(f"   🚗 打车服务:")
                        print(f"      服务状态: 已安排")
                        if "order_id" in data:
                            print(f"      订单号: {data.get('order_id')}")
                        if "driver_info" in data:
                            print(f"      司机信息: {data.get('driver_info')}")

                else:
                    print(f"   ❌ 执行失败: {function_response.get('error', '未知错误')}")
            else:
                print(f"   📄 原始响应: {function_response}")

        print("="*80)

    def create_task(self, function_name: str, function_args: dict, session_id: str) -> str:
        """创建新任务并添加到待确认任务列表"""
        self.task_counter += 1
        task_id = f"task_{self.task_counter}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 确定函数容错率
        fault_tolerance = "低" if function_name in ["call_taxi_service", "mcp_estimate_taxi_price"] else "高"

        # 创建任务参数
        parameters = {}
        required_params = self._get_required_params_for_function(function_name)

        for param_name in required_params:
            if param_name in function_args:
                value = function_args[param_name]
                if value is None or (isinstance(value, str) and value.strip() == ""):
                    state = TaskParameterState.NOT_FILLED
                else:
                    state = TaskParameterState.NOT_VALIDATED
            else:
                value = None
                state = TaskParameterState.NOT_FILLED

            parameters[param_name] = TaskParameter(
                name=param_name,
                value=value,
                state=state
            )

        # 创建任务信息
        task_info = TaskInfo(
            task_id=task_id,
            function_name=function_name,
            parameters=parameters,
            fault_tolerance=fault_tolerance,
            created_time=datetime.now().isoformat(),
            updated_time=datetime.now().isoformat()
        )

        # 添加到待确认任务列表
        self.pending_tasks[task_id] = task_info

        print(f"📋 创建任务: {task_id} ({function_name}, 容错率: {fault_tolerance})")
        return task_id

    def update_task_parameter(self, task_id: str, param_name: str, value: Any,
                            validation_result: Optional[Dict] = None) -> bool:
        """更新任务参数"""
        if task_id not in self.pending_tasks:
            return False

        task = self.pending_tasks[task_id]
        if param_name not in task.parameters:
            return False

        # 更新参数值和状态
        param = task.parameters[param_name]
        param.value = value
        param.validation_result = validation_result

        # 根据值和验证结果确定状态
        if value is None or (isinstance(value, str) and value.strip() == ""):
            param.state = TaskParameterState.NOT_FILLED
        elif validation_result is None:
            param.state = TaskParameterState.NOT_VALIDATED
        elif validation_result.get("is_valid", False):
            param.state = TaskParameterState.VALIDATED_PENDING
            param.confusion_score = validation_result.get("confusion_score")
            param.candidates = validation_result.get("candidates")
        else:
            param.state = TaskParameterState.NOT_VALIDATED

        # 更新任务时间
        task.updated_time = datetime.now().isoformat()

        print(f"📝 更新任务参数: {task_id}.{param_name} = {value} ({param.state.value})")
        return True

    def confirm_parameter(self, task_id: str, param_name: str) -> bool:
        """确认参数 - 将参数状态置为"已确认" """
        if task_id not in self.pending_tasks:
            return False

        task = self.pending_tasks[task_id]
        if param_name not in task.parameters:
            return False

        param = task.parameters[param_name]
        if param.state == TaskParameterState.VALIDATED_PENDING:
            param.state = TaskParameterState.CONFIRMED
            task.updated_time = datetime.now().isoformat()

            print(f"✅ 确认参数: {task_id}.{param_name}")
            return True

        return False

    def cancel_parameter(self, task_id: str, param_name: str) -> bool:
        """取消参数 - 将参数状态置为"未填写" """
        if task_id not in self.pending_tasks:
            return False

        task = self.pending_tasks[task_id]
        if param_name not in task.parameters:
            return False

        param = task.parameters[param_name]
        param.value = None
        param.state = TaskParameterState.NOT_FILLED
        param.validation_result = None
        param.confusion_score = None
        param.candidates = None
        task.updated_time = datetime.now().isoformat()

        print(f"❌ 取消参数: {task_id}.{param_name}")
        return True

    def check_task_executable(self, task_id: str) -> Dict:
        """检查任务是否可执行"""
        if task_id not in self.pending_tasks:
            return {"can_execute": False, "error": "任务不存在"}

        task = self.pending_tasks[task_id]

        # 检查所有必填参数
        missing_params = []
        unvalidated_params = []
        unconfirmed_params = []

        for param_name, param in task.parameters.items():
            if param.state == TaskParameterState.NOT_FILLED:
                missing_params.append(param_name)
            elif param.state == TaskParameterState.NOT_VALIDATED:
                unvalidated_params.append(param_name)
            elif param.state == TaskParameterState.VALIDATED_PENDING:
                if task.fault_tolerance == "低":
                    unconfirmed_params.append(param_name)

        can_execute = (len(missing_params) == 0 and
                      len(unvalidated_params) == 0 and
                      len(unconfirmed_params) == 0)

        return {
            "can_execute": can_execute,
            "missing_params": missing_params,
            "unvalidated_params": unvalidated_params,
            "unconfirmed_params": unconfirmed_params,
            "fault_tolerance": task.fault_tolerance
        }

    def execute_task(self, task_id: str) -> Dict:
        """执行任务"""
        if task_id not in self.pending_tasks:
            return {"status": False, "error": "任务不存在"}

        task = self.pending_tasks[task_id]

        # 检查是否可执行
        executable_check = self.check_task_executable(task_id)
        if not executable_check["can_execute"]:
            return {
                "status": False,
                "error": "任务不满足执行条件",
                "details": executable_check
            }

        # 准备执行参数
        execution_args = {}
        for param_name, param in task.parameters.items():
            execution_args[param_name] = param.value

        # 执行函数
        try:
            task.status = "executing"
            task.execution_time = datetime.now().isoformat()

            result = self._execute_function(task.function_name, execution_args, "task_execution")

            task.execution_result = result
            task.status = "completed" if result.get("status") else "failed"

            # 创建已执行任务的唯一键
            task_key = f"{task.function_name}_{task.execution_time}"

            # 根据容错率决定是否移动到已执行列表
            if task.fault_tolerance == "高":
                # 高容错率任务直接移动到已执行列表
                self.executed_tasks[task_key] = task
                del self.pending_tasks[task_id]
                print(f"✅ 高容错率任务执行完成，移动到已执行列表: {task_id}")
            else:
                # 低容错率任务需要用户确认后才移动
                print(f"⏳ 低容错率任务执行完成，等待用户确认: {task_id}")

            return {
                "status": True,
                "result": result,
                "task_key": task_key if task.fault_tolerance == "高" else None,
                "needs_confirmation": task.fault_tolerance == "低"
            }

        except Exception as e:
            task.status = "failed"
            task.execution_result = {"status": False, "error": str(e)}

            return {
                "status": False,
                "error": f"任务执行失败: {str(e)}"
            }

    def confirm_important_task(self, task_id: str) -> bool:
        """确认重要任务（低容错率）并移动到已执行列表"""
        if task_id not in self.pending_tasks:
            return False

        task = self.pending_tasks[task_id]

        if task.fault_tolerance != "低":
            return False

        if task.status != "completed":
            return False

        # 创建已执行任务的唯一键
        task_key = f"{task.function_name}_{task.execution_time}"

        # 移动到已执行列表
        self.executed_tasks[task_key] = task
        del self.pending_tasks[task_id]

        print(f"✅ 低容错率任务确认完成，移动到已执行列表: {task_id}")
        return True

    def get_pending_tasks(self) -> Dict[str, Dict]:
        """获取待确认任务列表"""
        result = {}
        for task_id, task in self.pending_tasks.items():
            result[task_id] = {
                "function_name": task.function_name,
                "fault_tolerance": task.fault_tolerance,
                "status": task.status,
                "created_time": task.created_time,
                "parameters": {
                    name: {
                        "value": param.value,
                        "state": param.state.value,
                        "confusion_score": param.confusion_score
                    }
                    for name, param in task.parameters.items()
                },
                "executable": self.check_task_executable(task_id)["can_execute"]
            }
        return result

    def get_executed_tasks(self) -> Dict[str, Dict]:
        """获取已执行任务列表"""
        result = {}
        for task_key, task in self.executed_tasks.items():
            result[task_key] = {
                "function_name": task.function_name,
                "fault_tolerance": task.fault_tolerance,
                "execution_time": task.execution_time,
                "execution_result": task.execution_result,
                "status": task.status
            }
        return result

    def check_executable_tasks(self) -> List[str]:
        """检查当前有哪些任务处于可执行状态"""
        executable_tasks = []
        for task_id in self.pending_tasks:
            if self.check_task_executable(task_id)["can_execute"]:
                executable_tasks.append(task_id)
        return executable_tasks

    def _handle_function_call_with_task_management(self, function_name: str, function_args: dict, session_id: str) -> str:
        """处理function call并集成任务管理"""
        # 查找是否已有相同函数的待确认任务
        existing_task_id = None
        for task_id, task in self.pending_tasks.items():
            if task.function_name == function_name and task.status == "pending":
                existing_task_id = task_id
                break

        if existing_task_id:
            # 更新现有任务
            task_id = existing_task_id
            task = self.pending_tasks[task_id]

            # 更新参数
            for param_name, value in function_args.items():
                if param_name in task.parameters:
                    # 验证参数（如果需要）
                    validation_result = None
                    if function_name in ["call_taxi_service"] and param_name in ["start_place", "end_place"]:
                        validation_result = self._validate_location_with_poi(value)

                    self.update_task_parameter(task_id, param_name, value, validation_result)
        else:
            # 创建新任务
            task_id = self.create_task(function_name, function_args, session_id)

            # 对需要验证的参数进行验证
            if function_name in ["call_taxi_service"]:
                task = self.pending_tasks[task_id]
                for param_name in ["start_place", "end_place"]:
                    if param_name in function_args and function_args[param_name]:
                        validation_result = self._validate_location_with_poi(function_args[param_name])
                        self.update_task_parameter(task_id, param_name, function_args[param_name], validation_result)

        return task_id

    def _handle_confirm_parameter(self, function_args: dict, session_id: str) -> dict:
        """处理confirm_parameter函数调用"""
        task_id = function_args.get("task_id")
        param_name = function_args.get("param_name")

        if not task_id or not param_name:
            return {"status": False, "error": "缺少task_id或param_name参数"}

        success = self.confirm_parameter(task_id, param_name)

        if success:
            return {
                "status": True,
                "message": f"参数 {param_name} 已确认",
                "task_id": task_id
            }
        else:
            return {
                "status": False,
                "error": f"确认参数失败: {task_id}.{param_name}"
            }

    def _handle_cancel_parameter(self, function_args: dict, session_id: str) -> dict:
        """处理cancel_parameter函数调用"""
        task_id = function_args.get("task_id")
        param_name = function_args.get("param_name")

        if not task_id or not param_name:
            return {"status": False, "error": "缺少task_id或param_name参数"}

        success = self.cancel_parameter(task_id, param_name)

        if success:
            return {
                "status": True,
                "message": f"参数 {param_name} 已取消",
                "task_id": task_id
            }
        else:
            return {
                "status": False,
                "error": f"取消参数失败: {task_id}.{param_name}"
            }

    def _check_and_execute_ready_tasks(self, session_id: str):
        """检查并执行准备就绪的任务"""
        executable_tasks = self.check_executable_tasks()

        for task_id in executable_tasks:
            print(f"🔄 检测到可执行任务: {task_id}")
            execution_result = self.execute_task(task_id)

            if execution_result["status"]:
                # 将执行结果添加到上下文
                task = self.pending_tasks.get(task_id) or self.executed_tasks.get(f"{self.pending_tasks[task_id].function_name}_{self.pending_tasks[task_id].execution_time}")
                if task:
                    result_content = json.dumps(execution_result["result"], ensure_ascii=False)
                    self.context[session_id].append({
                        "role": "assistant",
                        "content": f"任务 {task_id} 执行完成: {result_content}"
                    })
                    print(f"✅ 任务执行结果已添加到上下文: {task_id}")

    def _format_candidate_info_for_prompt(self, candidate_info: dict) -> str:
        """将候选信息格式化为prompt"""
        prompt_info = "\n\n📍 地理位置验证结果:"

        for param_name, info in candidate_info.items():
            value = info.get("value", "")
            confusion_score = info.get("confusion_score", 0.0)
            candidates = info.get("candidates", [])

            prompt_info += f"\n参数 {param_name}: '{value}' (困惑度: {confusion_score:.2f})"

            if candidates:
                prompt_info += f"\n找到的候选地点:"
                for i, candidate in enumerate(candidates, 1):
                    name = candidate.get("name", "未知")
                    address = candidate.get("address", "未知地址")
                    distance = candidate.get("distance", "")
                    distance_info = f" (距离: {distance}米)" if distance else ""
                    prompt_info += f"\n  {i}. {name} - {address}{distance_info}"

        prompt_info += "\n\n💡 请根据候选信息确认用户的具体意图，并询问用户确认执行方案。"
        prompt_info += f"\n当前城市环境: {DEFAULT_CITY}"

        return prompt_info

    def _generate_system_prompt_for_response(self, validation_result: dict) -> str:
        """根据验证结果生成系统提示词"""
        base_prompt = "请保持回复简洁明了，言简意赅，不要重复信息。"

        if not validation_result.get("can_execute"):
            action_type = validation_result.get("action_type", "")

            if action_type == "provide_parameters":
                return base_prompt + "用户缺少必要参数，请友好地询问缺少的信息。"

            elif action_type == "fix_parameters":
                return base_prompt + "用户提供的地理位置信息存在歧义或错误，请引导用户提供更准确的位置信息。如果存在多个候选式，询问用户是哪个"

            elif action_type == "confirm_with_candidates":
                return base_prompt + f"""
            用户的地理位置参数验证通过（困惑度≤0.2），现在需要确认执行方案。

            处理策略：
            1. 根据候选地点信息，向用户确认具体的地理位置
            2. 如果是打车服务，提供详细的路线和费用信息
            3. 询问用户是否确认执行该方案
            4. 语气要自然友好，像真人对话一样

            当前城市环境：{DEFAULT_CITY}
            请结合候选信息和用户意图，生成确认性的回复。"""
        else:
            return base_prompt + "函数执行成功，请根据执行结果给用户简洁明了的回复。"

    def _validate_function_by_type(self, function_name: str, function_args: dict, session_id: str) -> dict:
        """根据函数类型进行不同的验证策略"""
        try:
            # 定义需要校验的函数
            validation_required_functions = ["call_taxi_service"]

            if function_name in validation_required_functions:
                # 需要校验的函数：完整的参数验证流程
                return self._validate_with_poi_verification(function_name, function_args, session_id)
            else:
                # 不需要校验的函数：只检查完备性
                return self._validate_completeness_only(function_name, function_args, session_id)

        except Exception as e:
            return {
                "can_execute": False,
                "status": False,
                "error": f"验证过程出错: {str(e)}",
                "need_user_action": False
            }

    def _validate_with_poi_verification(self, function_name: str, function_args: dict, session_id: str) -> dict:
        """需要POI校验的函数验证流程"""
        try:
            # 1. 检查必填参数完整性
            required_params = self._get_required_params_for_function(function_name)
            missing_params = []
            for param in required_params:
                if (param not in function_args or
                    function_args[param] is None or
                    not function_args[param] or
                    str(function_args[param]).strip() == "" or
                    function_args[param] == "None"or
                    function_args[param] == "无") :
                    missing_params.append(param)

            if missing_params:
                return {
                    "can_execute": False,
                    "status": False,
                    "error": f"缺少必填参数: {', '.join(missing_params)}",
                    "missing_parameters": missing_params,
                    "need_user_action": True,
                    "action_type": "provide_parameters"
                }

            # 2. 对地理位置参数进行POI验证
            location_params = self._get_location_params_for_function(function_name)
            validation_results = {}
            all_valid = True

            for param_name in location_params:
                if param_name in function_args:
                    param_value = function_args[param_name]
                    poi_result = self._validate_location_with_poi(param_value)
                    validation_results[param_name] = poi_result

                    if not poi_result["is_valid"]:
                        all_valid = False

            # 3. 处理验证结果
            if not all_valid:
                # 有参数验证失败
                failed_params = []
                for param_name, result in validation_results.items():
                    if not result["is_valid"]:
                        confusion_score = result.get("confusion_score", 1.0)
                        if confusion_score > 0.2:
                            failed_params.append(f"{param_name}: 困惑度过高({confusion_score:.2f})")
                        else:
                            failed_params.append(f"{param_name}: {result.get('error', '验证失败')}")

                return {
                    "can_execute": False,
                    "status": False,
                    "error": f"参数验证失败: {'; '.join(failed_params)}",
                    "validation_errors": failed_params,
                    "validation_results": validation_results,
                    "need_user_action": True,
                    "action_type": "fix_parameters"
                }

            # 4. 验证通过，检查困惑度并准备确认信息
            confirmation_needed = False
            candidate_info = {}

            for param_name, result in validation_results.items():
                confusion_score = result.get("confusion_score", 0.0)
                if confusion_score > 0.2:
                    # 困惑度低，获取候选信息
                    candidates = result.get("candidates", [])
                    print("candidates:",candidates)
                    if candidates:
                        candidate_info[param_name] = {
                            "value": function_args[param_name],
                            "confusion_score": confusion_score,
                            "candidates": candidates[:3]  # 取前3个候选
                        }
                        confirmation_needed = True

            if confirmation_needed:
                # 需要用户确认，将候选信息加入prompt
                return {
                    "can_execute": False,
                    "status": True,
                    "message": "参数验证通过，请确认执行方案",
                    "need_user_action": True,
                    "action_type": "confirm_with_candidates",
                    "function_name": function_name,
                    "function_args": function_args,
                    "candidate_info": candidate_info,
                    "validation_results": validation_results
                }
            else:
                # 可以直接执行
                return {"can_execute": True}

        except Exception as e:
            return {
                "can_execute": False,
                "status": False,
                "error": f"POI验证过程出错: {str(e)}",
                "need_user_action": False
            }

    def _validate_completeness_only(self, function_name: str, function_args: dict, session_id: str) -> dict:
        """只检查完备性的函数验证流程"""
        try:
            # 只检查必填参数是否完整
            required_params = self._get_required_params_for_function(function_name)
            missing_params = []

            for param in required_params:
                if (param not in function_args or
                    function_args[param] is None or
                    not function_args[param] or
                    str(function_args[param]).strip() == ""or
                    function_args[param] == "None" or
                    function_args[param] == "无"):
                    missing_params.append(param)

            if missing_params:
                return {
                    "can_execute": False,
                    "status": False,
                    "error": f"缺少必填参数: {', '.join(missing_params)}",
                    "missing_parameters": missing_params,
                    "need_user_action": True,
                    "action_type": "provide_parameters"
                }
            else:
                # 参数完整，可以执行
                return {"can_execute": True}

        except Exception as e:
            return {
                "can_execute": False,
                "status": False,
                "error": f"完备性检查出错: {str(e)}",
                "need_user_action": False
            }

    def _get_required_params_for_function(self, function_name: str) -> list:
        """获取函数的必填参数"""
        param_map = {
            "call_taxi_service": ["start_place", "end_place"],
            "mcp_geocode_address": ["address"],
            "mcp_get_city_code": ["city_name"],
            "mcp_search_poi": ["keyword"],
            "mcp_reverse_geocode_poi": ["longitude", "latitude"],
            "mcp_recommend_similar_poi": ["poi_name"],
            "mcp_calculate_driving_route": ["origin_lng", "origin_lat", "dest_lng", "dest_lat"],
            "mcp_calculate_poi_to_poi_route": ["origin_poi", "dest_poi"],
            "mcp_search_taxi_spots": ["location"],
            "mcp_estimate_taxi_price": ["origin_poi", "dest_poi"]
        }
        return param_map.get(function_name, [])

    def _get_location_params_for_function(self, function_name: str) -> list:
        """获取函数的地理位置参数"""
        location_param_map = {
            "call_taxi_service": ["start_place", "end_place"]
        }
        return location_param_map.get(function_name, [])

    def _validate_location_with_poi(self, location: str) -> dict:
        """使用mcp_search_poi验证地理位置"""
        try:
            from amap_mcp_tools import mcp_search_poi_with_segmentation

            # 使用POI搜索验证地理位置
            search_result = mcp_search_poi_with_segmentation(location)

            if not search_result.get("status"):
                return {
                    "is_valid": False,
                    "error": f"POI搜索失败: {search_result.get('error', '未知错误')}",
                    "confusion_score": 1.0
                }

            data = search_result.get("data", {})
            confusion_score = data.get("confusion_score", 0.0)
            pois = data.get("pois", [])
            print("pois:",pois)
            # 根据困惑度判断是否有效
            if confusion_score > 0.2:
                if location in [item["name"] for item in pois]:
                    return {
                        "is_valid": True,
                        "confusion_score": 0,
                        "candidates": pois[:3],  # 返回前3个候选,
                        "best_match": location
                    }
                else:
                    return {
                        "is_valid": False,
                        "error": f"地理位置困惑度过高: {confusion_score:.2f}",
                        "confusion_score": confusion_score,
                        "candidates": pois[:5]  # 返回前5个候选供参考
                    }
            else:
                return {
                    "is_valid": True,
                    "confusion_score": confusion_score,
                    "candidates": pois[:3],  # 返回前3个候选
                    "best_match": pois[0] if pois else None
                }

        except Exception as e:
            return {
                "is_valid": False,
                "error": f"POI验证出错: {str(e)}",
                "confusion_score": 1.0
            }

    def _validate_and_confirm_parameters(self, function_name: str, function_args: dict, session_id: str) -> dict:
        """
        验证和确认参数的完整流程

        Args:
            function_name: 函数名
            function_args: 函数参数
            session_id: 会话ID

        Returns:
            Dict: 验证结果
        """
        try:
            # 跳过确认函数本身的验证
            if function_name in ["confirm_parameter", "cancel_parameter"]:
                return {"can_execute": True}

            # 1. 初始化函数参数
            init_result = self.parameter_manager.initialize_function_parameters(function_name, function_args)
            if not init_result.get("status"):
                return {
                    "can_execute": False,
                    "status": False,
                    "error": init_result.get("error", "参数初始化失败"),
                    "need_user_action": False
                }

            # 2. 检查是否有缺失的必填参数
            missing_required = init_result.get("missing_required", [])
            if missing_required:
                return {
                    "can_execute": False,
                    "status": False,
                    "error": f"缺少必填参数: {', '.join(missing_required)}",
                    "missing_parameters": missing_required,
                    "need_user_action": True,
                    "action_type": "provide_parameters"
                }

            # 3. 验证所有参数
            validation_result = self.parameter_manager.validate_all_parameters(function_name)
            if not validation_result.get("status"):
                return {
                    "can_execute": False,
                    "status": False,
                    "error": validation_result.get("error", "参数验证失败"),
                    "need_user_action": False
                }

            # 4. 检查验证结果
            if not validation_result.get("all_valid"):
                validation_errors = []
                for param_name, result in validation_result.get("validation_results", {}).items():
                    if not result.get("is_valid"):
                        confusion_score = result.get("confusion_score", 0.0)
                        if confusion_score > 0.2:  # 困惑度大于0.2
                            validation_errors.append(f"{param_name}: 困惑度过高({confusion_score:.2f})")
                        else:
                            validation_errors.append(f"{param_name}: {result.get('validation_details', {}).get('error', '验证失败')}")

                return {
                    "can_execute": False,
                    "status": False,
                    "error": f"参数验证失败: {'; '.join(validation_errors)}",
                    "validation_errors": validation_errors,
                    "need_user_action": True,
                    "action_type": "fix_parameters"
                }

            # 5. 检查是否可以执行
            execution_check = self.parameter_manager.can_execute_function(function_name)
            if not execution_check.get("can_execute"):
                # 需要用户确认
                unconfirmed_params = execution_check.get("unconfirmed_params", [])
                if unconfirmed_params:
                    return {
                        "can_execute": False,
                        "status": True,
                        "message": f"请确认以下参数: {', '.join(unconfirmed_params)}",
                        "unconfirmed_parameters": unconfirmed_params,
                        "need_user_action": True,
                        "action_type": "confirm_parameters",
                        "function_name": function_name
                    }
                else:
                    return {
                        "can_execute": False,
                        "status": False,
                        "error": "参数状态不满足执行条件",
                        "execution_check": execution_check,
                        "need_user_action": True,
                        "action_type": "check_parameters"
                    }

            # 6. 所有检查通过，可以执行
            return {"can_execute": True}

        except Exception as e:
            return {
                "can_execute": False,
                "status": False,
                "error": f"参数验证过程出错: {str(e)}",
                "need_user_action": False
            }

    def _execute_function(self, function_name: str, args: dict, session_id: str) -> dict:
        """执行function并处理结果"""
        try:
            # 记录function调用
            self.state.log_execution(
                action=f"execute_function:{function_name}",
                status="processing",
                details={"args": args}
            )

            # 根据function name调用不同的工具
            if function_name == "mcp_geocode_address":
                result = mcp_geocode_address(
                    address=args["address"],
                    city=args.get("city")
                )
            elif function_name == "mcp_get_city_code":
                result = mcp_get_city_code(
                    city_name=args["city_name"]
                )
            elif function_name == "mcp_search_poi":
                result = mcp_search_poi(
                    keyword=args["keyword"],
                    city=args.get("city"),
                    types=args.get("types")
                )
            elif function_name == "mcp_reverse_geocode_poi":
                result = mcp_reverse_geocode_poi(
                    longitude=args["longitude"],
                    latitude=args["latitude"],
                    radius=args.get("radius", 1000)
                )
            elif function_name == "mcp_recommend_similar_poi":
                result = mcp_recommend_similar_poi(
                    poi_name=args["poi_name"],
                    city=args.get("city"),
                    radius=args.get("radius", 2000)
                )
            elif function_name == "mcp_calculate_driving_route":
                result = mcp_calculate_driving_route(
                    origin_lng=args["origin_lng"],
                    origin_lat=args["origin_lat"],
                    dest_lng=args["dest_lng"],
                    dest_lat=args["dest_lat"],
                    strategy=args.get("strategy", 10)
                )
            elif function_name == "mcp_calculate_poi_to_poi_route":
                result = mcp_calculate_poi_to_poi_route(
                    origin_poi=args["origin_poi"],
                    dest_poi=args["dest_poi"],
                    origin_city=args.get("origin_city"),
                    dest_city=args.get("dest_city"),
                    strategy=args.get("strategy", 10)
                )
            elif function_name == "mcp_search_taxi_spots":
                result = mcp_search_taxi_spots(
                    location=args["location"],
                    city=args.get("city"),
                    radius=args.get("radius", 1000)
                )
            elif function_name == "mcp_estimate_taxi_price":
                result = mcp_estimate_taxi_price(
                    origin_poi=args["origin_poi"],
                    dest_poi=args["dest_poi"],
                    origin_city=args.get("origin_city"),
                    dest_city=args.get("dest_city"),
                    car_type=args.get("car_type", "经济型")
                )
            elif function_name == "call_taxi_service":
                # 调用打车服务并统一输出格式
                raw_result = call_taxi_service(
                    start_place=args["start_place"],
                    end_place=args["end_place"],
                    car_prefer=args.get("car_prefer", "")
                )
                # 统一格式化输出
                result = self._format_taxi_service_result(raw_result)
            elif function_name == "confirm_parameter":
                # 确认参数
                result = self.parameter_manager.confirm_parameter(
                    function_name=args["function_name"],
                    param_name=args["parameter_name"]
                )
            elif function_name == "cancel_parameter":
                # 取消参数
                result = self.parameter_manager.cancel_parameter(
                    function_name=args["function_name"],
                    param_name=args["parameter_name"]
                )
            else:
                raise ValueError(f"未知的工具: {function_name}")

            # 记录成功状态
            self.state.log_execution(
                action=f"execute_function:{function_name}",
                status="success",
                details={"result": result}
            )

            return result

        except Exception as e:
            error_result = {"status": False, "error": str(e)}
            self.debug_agent.log_error(str(e), {
                "function": function_name,
                "args": args
            })
            self.state.log_execution(
                action=f"execute_function:{function_name}",
                status="failed",
                details={"error": str(e)}
            )
            return error_result

    def _handle_ambiguous_location(self, location: str, city: str = None) -> dict:
        """处理模糊地点，自动使用POI搜索获取推荐"""
        try:
            # 检测是否为模糊地点
            ambiguous_keywords = ["机场", "大悦城", "万达", "银泰", "商场", "医院", "火车站", "地铁站", "公园"]

            if any(keyword in location for keyword in ambiguous_keywords):
                print(f"检测到模糊地点：{location}，正在搜索具体位置...")

                # 使用POI搜索获取具体位置
                poi_result = self._execute_function(
                    "mcp_search_poi",
                    {"keyword": location, "city": city or "北京"},
                    "ambiguous_location_session"
                )

                if poi_result.get("status") and poi_result.get("data", {}).get("pois"):
                    pois = poi_result["data"]["pois"][:5]  # 取前5个结果

                    recommendations = []
                    for i, poi in enumerate(pois, 1):
                        recommendations.append({
                            "index": i,
                            "name": poi.get("name", ""),
                            "address": poi.get("address", ""),
                            "location": poi.get("location", "")
                        })

                    return {
                        "status": True,
                        "is_ambiguous": True,
                        "original_location": location,
                        "recommendations": recommendations,
                        "message": f"找到{len(recommendations)}个相关地点，请选择具体位置"
                    }
                else:
                    return {
                        "status": False,
                        "is_ambiguous": True,
                        "original_location": location,
                        "message": f"未找到与'{location}'相关的具体地点"
                    }
            else:
                return {
                    "status": True,
                    "is_ambiguous": False,
                    "original_location": location,
                    "message": "地点描述具体，无需进一步搜索"
                }

        except Exception as e:
            return {
                "status": False,
                "is_ambiguous": True,
                "original_location": location,
                "error": str(e),
                "message": f"处理模糊地点时出错：{str(e)}"
            }

    def _debug_function_call_pre(self, function_name: str, function_args: dict, session_id: str) -> dict:
        """Function执行前的调试分析"""
        debug_score = 1.0  # 默认正常
        issues = []
        should_execute = True
        fallback_response = None

        # 1. 功能解析错误检查 (score: -1)
        available_functions = [tool["function"]["name"] for tool in self.tools]
        if function_name not in available_functions:
            debug_score = -1
            issues.append(f"功能解析错误：未知功能 '{function_name}'")
            should_execute = False
            fallback_response = {
                "status": False,
                "error": f"抱歉，我不支持 '{function_name}' 功能。",
                "available_functions": available_functions
            }

        # 2. 参数缺失检查 (score: 0)
        elif self._check_missing_parameters(function_name, function_args):
            debug_score = 0
            missing_params = self._get_missing_parameters(function_name, function_args)
            issues.append(f"参数缺失：{missing_params}")
            should_execute = False
            fallback_response = {
                "status": False,
                "error": f"请提供必需参数：{', '.join(missing_params)}",
                "missing_parameters": missing_params
            }

        # 3. 参数合理性检查 (score: 0.5)
        elif self._check_parameter_validity(function_name, function_args):
            debug_score = 0.5
            validity_issues = self._get_parameter_validity_issues(function_name, function_args)
            issues.append(f"参数不合理：{validity_issues}")

            # 特殊处理：如果是模糊地点，尝试POI搜索
            if function_name == "call_taxi_service" and "模糊" in validity_issues:
                start_place = function_args.get("start_place", "")
                end_place = function_args.get("end_place", "")

                # 处理起点模糊地点
                if start_place:
                    start_result = self._handle_ambiguous_location(start_place)
                    if start_result.get("is_ambiguous") and start_result.get("recommendations"):
                        issues.append(f"起点'{start_place}'有多个选择，建议明确具体位置")

                # 处理终点模糊地点
                if end_place:
                    end_result = self._handle_ambiguous_location(end_place)
                    if end_result.get("is_ambiguous") and end_result.get("recommendations"):
                        issues.append(f"终点'{end_place}'有多个选择，建议明确具体位置")

            # 仍然尝试执行，但标记为可能失败

        # 记录调试信息
        self.debug_agent.log_function_debug({
            "phase": "pre_execution",
            "function_name": function_name,
            "function_args": function_args,
            "debug_score": debug_score,
            "issues": issues,
            "should_execute": should_execute,
            "session_id": session_id
        })

        return {
            "debug_score": debug_score,
            "issues": issues,
            "should_execute": should_execute,
            "fallback_response": fallback_response
        }

    def _debug_function_call_post(self, function_name: str, function_args: dict,
                                  function_response: dict, session_id: str):
        """Function执行后的调试分析"""
        debug_score = 1.0
        issues = []

        # 检查执行结果
        if not function_response.get("status", False):
            debug_score = 0.5
            error_msg = function_response.get("error", "未知错误")
            issues.append(f"功能调用失败：{error_msg}")

            # 分析失败原因并提供建议
            suggestions = self._analyze_failure_and_suggest(function_name, function_args, error_msg)
            if suggestions:
                issues.extend(suggestions)

        # 记录调试信息
        self.debug_agent.log_function_debug({
            "phase": "post_execution",
            "function_name": function_name,
            "function_args": function_args,
            "function_response": function_response,
            "debug_score": debug_score,
            "issues": issues,
            "session_id": session_id
        })

        return {
            "debug_score": debug_score,
            "issues": issues
        }

    def _format_taxi_service_result(self, raw_result: dict) -> dict:
        """统一格式化打车服务的返回结果"""
        try:
            # 检查原始结果格式
            if not isinstance(raw_result, dict):
                return {
                    "status": False,
                    "error": "打车服务返回格式错误",
                    "raw_result": raw_result
                }

            # 统一格式化
            formatted_result = {
                "status": raw_result.get("status", 0) == 1,  # 1表示成功
                "message": raw_result.get("action_ret_msg", ""),
                "error_code": raw_result.get("err_code", ""),
                "error_message": raw_result.get("err_msg", ""),
                "time_cost": raw_result.get("time_cost", 0),
                "details": {}
            }

            # 提取详细信息
            if "params" in raw_result:
                params = raw_result["params"]
                formatted_result["details"] = {
                    "pickup_name": params.get("pickup_name", ""),
                    "pickup_location": {
                        "longitude": params.get("pickup_l", ""),
                        "latitude": params.get("pickup_r", "")
                    },
                    "destination_name": params.get("dest_name", ""),
                    "destination_location": {
                        "longitude": params.get("dest_l", ""),
                        "latitude": params.get("dest_r", "")
                    },
                    "sub_id": params.get("sub_id", ""),
                    "output": params.get("output", "")
                }

            # 保留原始结果用于调试
            formatted_result["raw_data"] = raw_result

            return formatted_result

        except Exception as e:
            return {
                "status": False,
                "error": f"格式化打车服务结果时出错: {str(e)}",
                "raw_result": raw_result
            }

    def _check_missing_parameters(self, function_name: str, function_args: dict) -> bool:
        """检查是否有缺失的必需参数"""
        for tool in self.tools:
            if tool["function"]["name"] == function_name:
                required_params = tool["function"]["parameters"].get("required", [])
                for param in required_params:
                    if param not in function_args or not function_args[param]:
                        return True
        return False

    def _get_missing_parameters(self, function_name: str, function_args: dict) -> list:
        """获取缺失的参数列表"""
        missing = []
        for tool in self.tools:
            if tool["function"]["name"] == function_name:
                required_params = tool["function"]["parameters"].get("required", [])
                for param in required_params:
                    if param not in function_args or not function_args[param]:
                        missing.append(param)
        return missing

    def _check_parameter_validity(self, function_name: str, function_args: dict) -> bool:
        """检查参数合理性"""
        if function_name == "call_taxi_service":
            start_place = function_args.get("start_place", "")
            end_place = function_args.get("end_place", "")

            # 检查是否是不合理的地点
            unrealistic_places = ["火星", "金星", "月球", "太阳", "外太空", "银河系"]
            if any(place in start_place for place in unrealistic_places) or \
               any(place in end_place for place in unrealistic_places):
                return True

            # 检查是否是同一地点
            if start_place.strip() == end_place.strip():
                return True

        return False

    def _get_parameter_validity_issues(self, function_name: str, function_args: dict) -> str:
        """获取参数合理性问题描述，使用大模型智能判断"""
        if function_name == "call_taxi_service":
            start_place = function_args.get("start_place", "")
            end_place = function_args.get("end_place", "")

            # 使用大模型进行智能判断
            return self._llm_analyze_location_validity(start_place, end_place)

        return "参数不合理"

    def _llm_analyze_location_validity(self, start_place: str, end_place: str) -> str:
        """使用大模型分析地点有效性"""
        if not self.bailian_client:
            # 回退到基础检查
            unrealistic_places = ["火星", "金星", "月球", "太阳", "外太空", "银河系"]
            if any(place in start_place for place in unrealistic_places) or \
               any(place in end_place for place in unrealistic_places):
                return "目的地超出服务范围（地球范围内）"
            if start_place.strip() == end_place.strip():
                return "起点和终点相同"
            return "参数可能不合理"

        try:
            prompt = f"""请分析以下打车路线的合理性：
            起点：{start_place}
            终点：{end_place}

            请判断：
            1. 这些地点是否是真实存在的地理位置？
            2. 是否适合打车服务？
            3. 如果不合理，具体原因是什么？

            如果地点模糊（如"机场"、"大悦城"），建议使用POI搜索获取具体位置。
            请简洁回答，只说明主要问题。"""

            response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=[
                    {'role': 'system', 'content': '你是一个地理位置分析专家，专门判断地点的合理性。'},
                    {'role': 'user', 'content': prompt}
                ]
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            # 回退到基础检查
            return f"无法验证地点合理性：{str(e)}"

    def _analyze_failure_and_suggest(self, function_name: str, function_args: dict, error_msg: str) -> list:
        """分析失败原因并提供建议"""
        suggestions = []

        if "网络" in error_msg or "连接" in error_msg:
            suggestions.append("建议：检查网络连接后重试")

        if "API" in error_msg or "密钥" in error_msg:
            suggestions.append("建议：检查API密钥配置")

        if function_name == "call_taxi_service":
            if "地点" in error_msg or "位置" in error_msg:
                suggestions.append("建议：请提供更具体的地址信息")

        if function_name.startswith("mcp_"):
            suggestions.append("建议：尝试使用更常见的地点名称")

        return suggestions

    def _optimize_response_brevity(self, response: str) -> str:
        """优化回复简洁性"""
        # 移除重复的句子
        sentences = response.split('。')
        unique_sentences = []
        seen = set()

        for sentence in sentences:
            sentence = sentence.strip()
            if sentence and sentence not in seen:
                unique_sentences.append(sentence)
                seen.add(sentence)

        # 简化常见表达
        optimized = '。'.join(unique_sentences)

        # 替换冗长表达
        replacements = {
            "非常感谢您的": "感谢",
            "如果您还有其他需要帮助的地方": "如需其他帮助",
            "请随时告诉我": "请告知",
            "我很乐意为您": "我将为您",
            "为了更好地为您提供帮助": "为了帮助您",
            "请您提供": "请提供",
            "如果您需要": "如需",
            "我可以帮助您": "我可以帮您"
        }

        for old, new in replacements.items():
            optimized = optimized.replace(old, new)

        return optimized

    def _analyze_out_of_scope_request(self, request: str) -> str:
        """使用大模型和联网搜索分析超出范围的请求"""
        if not self.bailian_client:
            # 回退到基础分析
            return self._basic_scope_analysis(request)

        try:
            # 使用大模型进行智能分析
            prompt = f"""请分析以下用户请求是否属于打车服务范围：

                    用户请求：{request}

                    打车服务范围包括：
                    1. 地点查询（地理编码、POI搜索）
                    2. 打车叫车服务
                    3. 城市代码查询

                    请判断：
                    1. 这个请求是否在服务范围内？
                    2. 如果不在，属于什么类型的请求？
                    3. 是否可以通过现有工具部分满足？

                    请简洁回答分析结果。"""

            response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=[
                    {'role': 'system', 'content': '你是一个服务范围分析专家。'},
                    {'role': 'user', 'content': prompt}
                ],
                extra_body={
                    "enable_search": True  # 启用联网搜索
                }
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            return f"分析失败，回退到基础判断：{self._basic_scope_analysis(request)}"

    def _basic_scope_analysis(self, request: str) -> str:
        """基础的范围分析（无需大模型）"""
        request_lower = request.lower()

        if any(keyword in request_lower for keyword in ["盈利", "财报", "股价", "业绩"]):
            return "财务信息查询 - 超出打车服务范围"
        elif any(keyword in request_lower for keyword in ["天气", "气温", "下雨"]):
            return "天气查询 - 超出打车服务范围"
        elif any(keyword in request_lower for keyword in ["电影", "音乐", "娱乐"]):
            return "娱乐推荐 - 超出打车服务范围"
        elif any(keyword in request_lower for keyword in ["机票", "酒店", "旅游"]):
            return "旅游服务 - 超出打车服务范围"
        else:
            return "未知请求类型 - 可能超出服务范围"

    def _provide_intelligent_suggestions(self, request: str) -> str:
        """使用大模型和联网搜索提供智能建议"""
        if not self.bailian_client:
            # 回退到基础建议
            return self._basic_suggestions(request)

        try:
            # 使用大模型生成智能建议
            prompt = f"""用户请求：{request}

这个请求超出了打车服务范围。请提供以下建议：

1. 推荐合适的工具、网站或APP来解决用户需求
2. 如果存在相关的MCP工具或API，建议用户配置使用
3. 评估是否可以在未来版本中支持此功能
4. 提供具体的操作步骤

请提供实用、具体的建议，包括推荐的平台名称和使用方法。"""

            response = self.bailian_client.chat.completions.create(
                model=self.bailian_model_name,
                messages=[
                    {'role': 'system', 'content': '你是一个智能助手建议专家，专门为用户提供替代解决方案。'},
                    {'role': 'user', 'content': prompt}
                ],
                extra_body={
                    "enable_search": True  # 启用联网搜索获取最新信息
                }
            )

            return response.choices[0].message.content.strip()

        except Exception as e:
            return f"智能建议生成失败，基础建议：{self._basic_suggestions(request)}"

    def _basic_suggestions(self, request: str) -> str:
        """基础建议（无需大模型）"""
        analysis = self._basic_scope_analysis(request)

        if "财务信息" in analysis:
            return "建议使用专业财经网站或APP查询企业财务信息，如东方财富、同花顺等"
        elif "天气查询" in analysis:
            return "建议使用天气APP或询问语音助手获取天气信息"
        elif "娱乐推荐" in analysis:
            return "建议使用豆瓣、猫眼等娱乐平台获取推荐"
        elif "旅游服务" in analysis:
            return "建议使用携程、去哪儿等旅游平台预订机票酒店"
        else:
            return "抱歉，我专注于打车和地点查询服务。如需其他帮助，建议使用相应的专业平台"

    def _handle_scene_function(self, function_name: str, args: dict) -> dict:
        """处理场景相关的function调用"""
        # 这里可以根据不同场景实现不同逻辑
        return {
            "status": True,
            "data": args,
            "message": f"成功处理场景: {function_name}"
        }

    # 保持其他原有方法不变...
    def process_user_input(self, user_input: str) -> str:
        # 原有实现...
        pass

    def _detect_intent(self, text: str) -> str:
        # 原有实现...
        pass

    def _extract_parameters(self, text: str, intent: str) -> Dict:
        # 原有实现...
        pass

    def _check_missing_params(self, intent: str, params: Dict) -> Dict:
        # 原有实现...
        pass

    def _prepare_params(self, intent: str, params: Dict) -> Dict:
        # 原有实现...
        pass

    def _generate_success_response(self, intent: str, result: Dict) -> str:
        # 原有实现...
        pass

    def _handle_error(self, intent: str, error_result: Dict) -> str:
        # 原有实现...
        pass

    def _extract_nearby_poi_candidates(self, result: Dict) -> List[Dict]:
        """从经纬度转POI结果中提取候选项"""
        candidates = []
        if result.get("status") and result.get("data", {}).get("nearby_pois"):
            pois = result["data"]["nearby_pois"][:5]  # 取前5个
            for i, poi in enumerate(pois, 1):
                candidates.append({
                    "index": i,
                    "name": poi.get("name", ""),
                    "address": poi.get("address", ""),
                    "type": poi.get("type", ""),
                    "distance": poi.get("distance", 0),
                    "location": f"{poi.get('longitude', '')},{poi.get('latitude', '')}"
                })
        return candidates

    def _extract_similar_poi_candidates(self, result: Dict) -> List[Dict]:
        """从POI推荐结果中提取候选项"""
        candidates = []
        if result.get("status") and result.get("data", {}).get("similar_pois"):
            pois = result["data"]["similar_pois"][:5]  # 取前5个
            for i, poi in enumerate(pois, 1):
                candidates.append({
                    "index": i,
                    "name": poi.get("name", ""),
                    "address": poi.get("address", ""),
                    "type": poi.get("type", ""),
                    "distance": poi.get("distance", 0),
                    "similarity_score": poi.get("similarity_score", 0),
                    "location": f"{poi.get('longitude', '')},{poi.get('latitude', '')}"
                })
        return candidates

    def _analyze_reverse_geocode_result(self, result: Dict, user_intent: str) -> List[str]:
        """分析经纬度转POI结果"""
        suggestions = []
        if result.get("status"):
            data = result.get("data", {})
            poi_count = data.get("total_count", 0)
            if poi_count > 0:
                suggestions.append(f"在该位置附近找到{poi_count}个兴趣点")
                if poi_count > 5:
                    suggestions.append("显示了最相关的前5个结果")
            else:
                suggestions.append("该位置附近暂无已知的兴趣点")
        else:
            suggestions.append("无法获取该位置的周边信息")
        return suggestions

    def _analyze_poi_recommendation_result(self, result: Dict, user_intent: str) -> List[str]:
        """分析POI推荐结果"""
        suggestions = []
        if result.get("status"):
            data = result.get("data", {})
            original_poi = data.get("original_poi", {})
            similar_count = len(data.get("similar_pois", []))

            if similar_count > 0:
                suggestions.append(f"基于'{original_poi.get('name', '')}'找到{similar_count}个相似地点")
                suggestions.append("推荐结果按相似度排序")
            else:
                suggestions.append(f"'{original_poi.get('name', '')}'附近暂无相似的地点")
        else:
            suggestions.append("无法找到相似的地点推荐")
        return suggestions

class MainAgent:
    """保持向后兼容的主控Agent"""
    def __init__(self):
        self.enhanced_agent = EnhancedTaxiAgent()

    def process_user_input(self, user_input: str, session_id: str = "default") -> str:
        """处理用户输入，支持会话ID"""
        return self.enhanced_agent.process_message(user_input, session_id)

    def start_new_session(self, session_id: str = None) -> str:
        """开始新的会话"""
        import time
        if session_id is None:
            session_id = f"session_{int(time.time())}"

        # 清除该会话的上下文（如果存在）
        if hasattr(self.enhanced_agent, 'context') and session_id in self.enhanced_agent.context:
            del self.enhanced_agent.context[session_id]

        # 重置该会话的状态
        if hasattr(self.enhanced_agent, 'state'):
            # 清除会话相关的状态
            self.enhanced_agent.state.conversation_state.pop(session_id, None)

        return session_id

    def get_session_info(self, session_id: str = "default") -> dict:
        """获取会话信息"""
        info = {
            "session_id": session_id,
            "has_context": False,
            "message_count": 0,
            "last_activity": None
        }

        if hasattr(self.enhanced_agent, 'context') and session_id in self.enhanced_agent.context:
            context = self.enhanced_agent.context[session_id]
            info["has_context"] = True
            info["message_count"] = len([msg for msg in context if msg.get("role") in ["user", "assistant"]])

        return info


def test_enhanced_taxi_agent():
    """测试增强版打车Agent"""
    print("=== 测试增强版打车Agent ===")

    agent = EnhancedTaxiAgent()

    # 测试用例
    test_cases = [
        "西湖在哪里？",
        "帮我查一下杭州的城市代码",
        "北京有哪些星巴克？",
        "经纬度116.397428,39.90923附近有什么？",
        "推荐一些北京上地星巴克附近的咖啡店",
        "我要从康德大厦打车到太阳宫",
        "从北京站到首都机场，要舒适型车辆"
    ]

    for i, test_input in enumerate(test_cases, 1):
        print(f"\n--- 测试 {i}: {test_input} ---")
        try:
            response = agent.process_message(test_input, f"test_session_{i}")
            print(f"回复: {response}")
        except Exception as e:
            print(f"错误: {e}")
        print("-" * 50)


def test_call_taxi_service_format():
    """测试 call_taxi_service 的格式化输出"""
    print("\n=== 测试 call_taxi_service 格式化输出 ===")

    agent = EnhancedTaxiAgent()

    # 直接测试打车服务
    try:
        result = agent._execute_function(
            "call_taxi_service",
            {
                "start_place": "康德大厦",
                "end_place": "太阳",
                "car_prefer": ""
            },
            "test_session"
        )

        print("格式化后的结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))

    except Exception as e:
        print(f"测试失败: {e}")


def test_multi_turn_conversation():
    """测试多轮对话场景"""
    print("\n=== 多轮对话测试 ===")

    agent = MainAgent()
    session_id = "multi_turn_test"

    # 定义多轮对话场景
    conversation_scenarios = [
        {
            "name": "完整打车流程",
            "turns": [
                "我想打车",
                "从康德大厦",
                "到大悦城",
                "要舒适型车辆",
                "确认"
            ]
        },
        {
            "name": "地点查询和导航",
            "turns": [
                "西湖在哪里？",
                "从西湖到杭州东站怎么走？",
                "大概需要多长时间？",
                "打车费用大概多少？"
            ]
        },
        {
            "name": "POI搜索和推荐",
            "turns": [
                "北京有哪些星巴克？",
                "推荐上地附近的咖啡店",
                "哪个离地铁站最近？",
                "帮我查一下营业时间"
            ]
        },
        {
            "name": "错误处理和重试",
            "turns": [
                "我要去火星",
                "我要去北京",
                "从天安门",
                "到鸟巢",
                "现在就要"
            ]
        },
        {
            "name": "参数确认流程",
            "turns": [
                "帮我叫车",
                "从北京大学",
                "到清华大学",
                "要豪华型",
                "确认起点",
                "确认终点",
                "确认车型",
                "开始叫车"
            ]
        }
    ]

    for scenario in conversation_scenarios:
        print(f"\n{'='*60}")
        print(f"场景: {scenario['name']}")
        print(f"{'='*60}")

        # 为每个场景使用独立的session
        scenario_session = f"{session_id}_{scenario['name']}"

        for turn_num, user_input in enumerate(scenario['turns'], 1):
            print(f"\n第{turn_num}轮:")
            print(f"用户: {user_input}")

            try:
                response = agent.process_user_input(user_input, scenario_session)
                print(f"助手: {response}")

                # 添加短暂延迟模拟真实对话
                import time
                time.sleep(0.5)

            except Exception as e:
                print(f"错误: {e}")

            print("-" * 40)

        print(f"\n场景 '{scenario['name']}' 完成\n")


def test_session_management():
    """测试会话管理功能"""
    print("\n=== 会话管理测试 ===")

    agent = MainAgent()

    # 测试多个并发会话
    sessions = ["user_1", "user_2", "user_3"]

    for session in sessions:
        print(f"\n--- 会话 {session} ---")

        # 每个会话的不同请求
        requests = [
            "我在哪里？",
            "附近有什么好吃的？",
            "帮我叫个车"
        ]

        for request in requests:
            print(f"用户({session}): {request}")
            try:
                response = agent.process_user_input(request, session)
                print(f"助手: {response}")
            except Exception as e:
                print(f"错误: {e}")

        print(f"会话 {session} 结束")


def test_context_continuity():
    """测试上下文连续性"""
    print("\n=== 上下文连续性测试 ===")

    agent = MainAgent()
    session_id = "context_test"

    # 测试上下文记忆
    context_flow = [
        "我想去西湖",
        "那里怎么样？",  # 应该知道"那里"指西湖
        "从那里到杭州东站怎么走？",  # 应该知道起点是西湖
        "打车要多少钱？",  # 应该记住路线
        "好的，帮我叫车",  # 应该记住起点终点
        "取消订单",  # 应该知道要取消什么
    ]

    print("测试对话上下文的连续性...")

    for turn, user_input in enumerate(context_flow, 1):
        print(f"\n第{turn}轮:")
        print(f"用户: {user_input}")

        try:
            response = agent.process_user_input(user_input, session_id)
            print(f"助手: {response}")
        except Exception as e:
            print(f"错误: {e}")


def test_error_recovery():
    """测试错误恢复能力"""
    print("\n=== 错误恢复测试 ===")

    agent = MainAgent()
    session_id = "error_test"

    # 测试各种错误场景
    error_scenarios = [
        {
            "name": "无效地点",
            "inputs": ["我要去火星", "我要去北京天安门"]
        },
        {
            "name": "缺少参数",
            "inputs": ["我要打车", "从北京大学", "到清华大学"]
        },
        {
            "name": "模糊地点",
            "inputs": ["我要去大厦", "康德大厦", "确认"]
        },
        {
            "name": "取消操作",
            "inputs": ["帮我叫车", "取消", "重新开始"]
        }
    ]

    for scenario in error_scenarios:
        print(f"\n--- 错误场景: {scenario['name']} ---")

        for user_input in scenario['inputs']:
            print(f"用户: {user_input}")
            try:
                response = agent.process_user_input(user_input, session_id)
                print(f"助手: {response}")
            except Exception as e:
                print(f"系统错误: {e}")
            print()


def test_performance_monitoring():
    """测试性能监控"""
    print("\n=== 性能监控测试 ===")

    agent = MainAgent()

    # 记录开始时间
    import time
    start_time = time.time()

    # 执行一系列请求
    test_requests = [
        "西湖在哪里？",
        "北京有哪些星巴克？",
        "从天安门到鸟巢怎么走？",
        "帮我叫车从康德大厦到大悦城",
        "取消订单"
    ]

    response_times = []

    for i, request in enumerate(test_requests, 1):
        request_start = time.time()

        print(f"请求 {i}: {request}")
        try:
            response = agent.process_user_input(request, f"perf_test_{i}")
            request_time = time.time() - request_start
            response_times.append(request_time)

            print(f"响应: {response}")
            print(f"响应时间: {request_time:.2f}秒")

        except Exception as e:
            print(f"错误: {e}")

        print("-" * 30)

    # 统计信息
    total_time = time.time() - start_time
    avg_response_time = sum(response_times) / len(response_times) if response_times else 0

    print(f"\n性能统计:")
    print(f"总测试时间: {total_time:.2f}秒")
    print(f"平均响应时间: {avg_response_time:.2f}秒")
    print(f"最快响应: {min(response_times):.2f}秒" if response_times else "无数据")
    print(f"最慢响应: {max(response_times):.2f}秒" if response_times else "无数据")


if __name__ == "__main__":
    # 测试增强版Agent
    # test_enhanced_taxi_agent()

    # # 测试打车服务格式化
    # test_call_taxi_service_format()

    # 保持向后兼容
    print("\n=== 向后兼容测试 ===")
    agent = MainAgent()
    print(agent.process_user_input("我要从康德大厦打车到大悦城"))

    # 运行多轮对话测试
    test_multi_turn_conversation()

    # 运行会话管理测试
    test_session_management()

    # 运行上下文连续性测试
    test_context_continuity()

    # 运行错误恢复测试
    test_error_recovery()

    # 运行性能监控测试
    test_performance_monitoring()
